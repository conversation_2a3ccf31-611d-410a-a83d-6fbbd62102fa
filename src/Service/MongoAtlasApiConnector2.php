<?php

namespace App\Service;

use App\Helpers\WSResponse;
use App\Helpers\LoggerTrait;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * The connector with the second AtlasMongoDB API.
 */
class MongoAtlasApiConnector2
{
    use LoggerTrait;

    private HttpClientInterface $client;

    public function __construct(
        HttpClientInterface $mongoAtlasClient, 
        private string $mongoApp
    )
    {
        $this->client = $mongoAtlasClient;
        $this->mongoApp = $mongoApp;
    }

    /**
     * Call Mongo Atlas api.
     *
     * @param mixed|array $options
     */
    public function call(string $method, string $url, mixed $options = []): WSResponse
    {
        try {
            $response = $this->client->request($method, $url, $options);
            $this->logger->info('MongoAtlasApiConnector2::call '.$url.' options '.json_encode($options));
            return new WSResponse($response->getStatusCode(), $response->getContent(false));
        } catch (\Exception $e) {
            $this->logger->error('error occured while calling '.$url.'error: '.$e->getMessage());
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * build mongoAtlas endpoint.
     */
    public function getEndpoint(string $action): string
    {
        return sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);
    }
}
