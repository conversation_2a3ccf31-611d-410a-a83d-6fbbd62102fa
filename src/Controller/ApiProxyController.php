<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;

class ApiProxyController extends AbstractController
{
    private HttpClientInterface $httpClient;
    private LoggerInterface $logger;

    public function __construct(HttpClientInterface $httpClient, LoggerInterface $logger)
    {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
    }

    #[Route('/api/proxy', name: 'api_proxy', methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])]
    public function proxy(Request $request): JsonResponse
    {
        try {
            // Get the target URL from request parameters or body
            $targetUrl = $request->query->get('url');
            $method = 'GET';
            $params = [];

            // If it's a POST request to the proxy, get data from body
            if ($request->getMethod() === 'POST' && $request->getContent()) {
                $requestData = json_decode($request->getContent(), true);
                if ($requestData) {
                    $method = $requestData['method'] ?? 'GET';
                    $params = $requestData['params'] ?? [];
                }
            }

            if (!$targetUrl) {
                return new JsonResponse([
                    'error' => [
                        'message' => 'Missing target URL parameter',
                        'type' => 'ValidationError'
                    ]
                ], Response::HTTP_BAD_REQUEST);
            }

            // Validate the target URL
            if (!filter_var($targetUrl, FILTER_VALIDATE_URL)) {
                return new JsonResponse([
                    'error' => [
                        'message' => 'Invalid target URL',
                        'type' => 'ValidationError'
                    ]
                ], Response::HTTP_BAD_REQUEST);
            }

            // Prepare headers
            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'Space-BO-Debug-Tool/1.0'
            ];

            // Add any custom headers from the request
            foreach ($request->headers->all() as $name => $values) {
                if (in_array(strtolower($name), ['authorization', 'x-api-key', 'x-auth-token'])) {
                    $headers[$name] = $values[0];
                }
            }

            // For GET requests, add parameters to URL
            if ($method === 'GET' && !empty($params)) {
                $separator = strpos($targetUrl, '?') !== false ? '&' : '?';
                $targetUrl .= $separator . http_build_query($params);
            }

            // Prepare request options
            $options = [
                'headers' => $headers,
                'timeout' => 30
            ];

            // Add body for non-GET requests
            if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($params)) {
                $options['body'] = json_encode($params);
            }

            $this->logger->info('API Proxy Request', [
                'method' => $method,
                'url' => $targetUrl,
                'headers' => array_keys($headers)
            ]);

            // Make the HTTP request
            $response = $this->httpClient->request($method, $targetUrl, $options);

            // Get response data
            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false); // false = don't throw on error status codes
            $responseHeaders = $response->getHeaders(false);

            $this->logger->info('API Proxy Response', [
                'status' => $statusCode,
                'url' => $targetUrl
            ]);

            // Try to decode JSON, fallback to raw content
            $jsonData = null;
            try {
                $jsonData = json_decode($content, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $jsonData = null;
                }
            } catch (\Exception $e) {
                $jsonData = null;
            }

            // Return the response
            return new JsonResponse([
                'status' => $statusCode,
                'headers' => $responseHeaders,
                'data' => $jsonData ?: $content,
                'raw_content' => $content
            ], $statusCode >= 400 ? Response::HTTP_OK : $statusCode);

        } catch (\Exception $e) {
            $this->logger->error('API Proxy Error', [
                'message' => $e->getMessage(),
                'url' => $targetUrl ?? 'unknown'
            ]);

            return new JsonResponse([
                'error' => [
                    'message' => $e->getMessage(),
                    'type' => get_class($e),
                    'code' => $e->getCode()
                ]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/api/proxy/test', name: 'api_proxy_test', methods: ['GET'])]
    public function test(): JsonResponse
    {
        return new JsonResponse([
            'message' => 'API Proxy is working',
            'timestamp' => date('c'),
            'version' => '1.0'
        ]);
    }

    #[Route('/api/proxy/simple', name: 'api_proxy_simple', methods: ['GET'])]
    public function simpleProxy(Request $request): JsonResponse
    {
        try {
            $targetUrl = $request->query->get('url');
            if (!$targetUrl) {
                return new JsonResponse(['error' => 'Missing URL parameter'], 400);
            }

            $this->logger->info('Simple API Proxy Request', ['url' => $targetUrl]);

            $response = $this->httpClient->request('GET', $targetUrl, [
                'headers' => [
                    'Accept' => 'application/json',
                    'User-Agent' => 'Space-BO-Debug-Tool/1.0'
                ],
                'timeout' => 30
            ]);

            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false);

            // Try to decode JSON
            $jsonData = null;
            try {
                $jsonData = json_decode($content, true);
            } catch (\Exception $e) {
                $jsonData = ['raw_content' => $content];
            }

            return new JsonResponse($jsonData ?: ['raw_content' => $content], 200);

        } catch (\Exception $e) {
            $this->logger->error('Simple API Proxy Error', ['message' => $e->getMessage()]);
            return new JsonResponse([
                'error' => [
                    'message' => $e->getMessage(),
                    'type' => get_class($e)
                ]
            ], 500);
        }
    }
}
