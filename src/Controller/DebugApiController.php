<?php


namespace App\Controller;

use App\Form\DebugApiFormType;
use App\Security\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;


#[Route(path: '/debug_api', name: 'debug_api_')]
class DebugApiController extends AbstractController
{

    #[Route(path: '/', name: 'form')]
    public function index(#[CurrentUser] User $user, Request $request): Response
    {
        $form = $this->createForm(DebugApiFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            dd($form->getData());
        }

        return $this->render('debug_api/index.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
