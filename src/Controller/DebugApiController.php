<?php


namespace App\Controller;

use App\Form\DebugApiFormType;
use App\Service\MongoAtlasQueryService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;


#[Route(path: '/debug_api', name: 'debug_api_')]
class DebugApiController extends AbstractController
{
    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {
    }

    #[Route(path: '/', name: 'form')]
    public function index(Request $request): Response
    {
        $form = $this->createForm(DebugApiFormType::class);
        $form->handleRequest($request);

        $mongoData = null;
        $vehicles = [];
        $error = null;

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $form->getData();

            // Build MongoDB filter based on form data
            $filter = $this->buildMongoFilter($formData);

            // Query the userData collection
            $result = $this->mongoService->find('userData', $filter);

            if ($result->getCode() === 200) {
                $mongoData = json_decode($result->getData(), true);

                // Extract vehicles from all documents with enhanced information
                if (isset($mongoData['documents']) && is_array($mongoData['documents'])) {
                    foreach ($mongoData['documents'] as $document) {
                        if (isset($document['vehicle']) && is_array($document['vehicle'])) {
                            foreach ($document['vehicle'] as $vehicle) {
                                $vehicles[] = [
                                    'vin' => $vehicle['vin'] ?? 'N/A',
                                    'label' => $this->generateVehicleLabel($vehicle, $document),
                                    'versionId' => $vehicle['versionId'] ?? 'N/A',
                                    'brand' => $vehicle['brand'] ?? 'N/A',
                                    'userId' => $document['userId'] ?? 'N/A',
                                    'userEmail' => $document['profile']['email'] ?? 'N/A',
                                    'userCountry' => $document['profile']['country'] ?? 'N/A',
                                    'userName' => ($document['profile']['firstName'] ?? '') . ' ' . ($document['profile']['lastName'] ?? ''),
                                    'mileage' => $vehicle['mileage']['value'] ?? 'N/A',
                                    'mileageUnit' => $vehicle['mileage']['unit'] ?? 'km',
                                    'featureCodesCount' => count($vehicle['featureCode'] ?? [])
                                ];
                            }
                        }
                    }
                }
            } else {
                $error = [
                    'message' => $result->getData(),
                    'code' => $result->getCode()
                ];
            }
        }

        return $this->render('debug_api/index.html.twig', [
            'form' => $form->createView(),
            'mongoData' => $mongoData,
            'vehicles' => $vehicles,
            'error' => $error,
            'jsonData' => $mongoData ? json_encode($mongoData) : null
        ]);
    }

    /**
     * Build MongoDB filter based on form data using the new document structure
     */
    private function buildMongoFilter(array $formData): array
    {
        $filter = [];
        $andConditions = [];

        // Filter by userId if provided
        if (!empty($formData['userID'])) {
            $andConditions[] = ['userId' => $formData['userID']];
        }

        // Filter by email if provided (search in profile.email)
        if (!empty($formData['email'])) {
            $andConditions[] = ['profile.email' => $formData['email']];
        }

        // Filter by VIN if provided (search in vehicle array)
        if (!empty($formData['vin'])) {
            $andConditions[] = ['vehicle.vin' => $formData['vin']];
        }

        // Filter by country if provided (search in profile.country)
        if (!empty($formData['countryFilter'])) {
            $andConditions[] = ['profile.country' => $formData['countryFilter']];
        }

        // If multiple conditions, use $and operator for combined filtering
        if (count($andConditions) > 1) {
            $filter['$and'] = $andConditions;
        } elseif (count($andConditions) === 1) {
            $filter = $andConditions[0];
        }

        return $filter;
    }

    /**
     * Generate a descriptive label for the vehicle
     */
    private function generateVehicleLabel(array $vehicle, array $document): string
    {
        $brand = $vehicle['brand'] ?? 'Unknown';
        $vin = $vehicle['vin'] ?? 'N/A';
        $userName = trim(($document['profile']['firstName'] ?? '') . ' ' . ($document['profile']['lastName'] ?? ''));

        if (empty($userName)) {
            $userName = 'User';
        }

        return "{$brand} Vehicle - {$userName} ({$vin})";
    }
}
