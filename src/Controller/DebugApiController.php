<?php


namespace App\Controller;

use App\Form\DebugApiFormType;
use App\Service\MongoAtlasQueryService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;


#[Route(path: '/debug_api', name: 'debug_api_')]
class DebugApiController extends AbstractController
{
    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {
    }

    #[Route(path: '/', name: 'form')]
    public function index(Request $request): Response
    {
        $form = $this->createForm(DebugApiFormType::class);
        $form->handleRequest($request);

        $mongoData = null;
        $vehicles = [];
        $error = null;

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $form->getData();

            // Build MongoDB filter based on form data
            $filter = $this->buildMongoFilter($formData);

            // Query the userData collection
            $result = $this->mongoService->find('userData', $filter);

            if ($result->getCode() === 200) {
                $mongoData = json_decode($result->getData(), true);

                // Extract vehicles from all documents
                if (isset($mongoData['documents']) && is_array($mongoData['documents'])) {
                    foreach ($mongoData['documents'] as $document) {
                        if (isset($document['vehicle']) && is_array($document['vehicle'])) {
                            foreach ($document['vehicle'] as $vehicle) {
                                $vehicles[] = [
                                    'vin' => $vehicle['vin'] ?? 'N/A',
                                    'label' => $vehicle['label'] ?? 'N/A',
                                    'versionId' => $vehicle['versionId'] ?? 'N/A',
                                    'brand' => $vehicle['brand'] ?? 'N/A',
                                    'userId' => $document['userId'] ?? 'N/A'
                                ];
                            }
                        }
                    }
                }
            } else {
                $error = [
                    'message' => $result->getData(),
                    'code' => $result->getCode()
                ];
            }
        }

        return $this->render('debug_api/index.html.twig', [
            'form' => $form->createView(),
            'mongoData' => $mongoData,
            'vehicles' => $vehicles,
            'error' => $error,
            'jsonData' => $mongoData ? json_encode($mongoData) : null
        ]);
    }

    /**
     * Build MongoDB filter based on form data
     */
    private function buildMongoFilter(array $formData): array
    {
        $filter = [];

        // Filter by userId if provided
        if (!empty($formData['userID'])) {
            $filter['userId'] = $formData['userID'];
        }

        // Filter by email if provided (assuming email is stored in a user profile or similar)
        if (!empty($formData['email'])) {
            // You might need to adjust this based on your document structure
            $filter['email'] = $formData['email'];
        }

        // Filter by VIN if provided (search in vehicle array)
        if (!empty($formData['vin'])) {
            $filter['vehicle.vin'] = $formData['vin'];
        }

        return $filter;
    }
}
