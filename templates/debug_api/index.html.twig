{% extends '_layout/layout.html.twig' %}

{% block title %}Debug API Form{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Force CSS refresh - Version 2.0 */
        /* JSON Viewer Styles */
        .json-viewer {
            color: #000;
            padding-left: 20px;
        }

        .json-viewer ul {
            list-style-type: none;
            margin: 0;
            margin: 0 0 0 1px;
            border-left: 1px dotted #ccc;
            padding-left: 2em;
        }

        .json-viewer .type-string {
            color: #0B7500;
        }

        .json-viewer .type-date {
            color: #CB7500;
        }

        .json-viewer .type-boolean {
            color: #1A01CC;
            font-weight: bold;
        }

        .json-viewer .type-number {
            color: #1A01CC;
        }

        .json-viewer .type-null {
            color: #90A;
        }

        .json-viewer a.list-link {
            color: #000;
            text-decoration: none;
            position: relative;
        }

        .json-viewer a.list-link:before {
            color: #aaa;
            content: "▶";
            position: absolute;
            left: -1em;
        }

        .json-viewer a.list-link.collapsed:before {
            content: "▼";
        }

        .json-viewer a.list-link.empty:before {
            content: "";
        }

        .json-viewer .items-ph {
            color: #aaa;
            cursor: pointer;
        }

        .json-viewer .items-ph:hover {
            text-decoration: underline;
        }
        .vehicle-card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .vehicle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .vehicle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .vehicle-card:hover::before {
            transform: scaleX(1);
        }

        .vehicle-card.selected {
            border: 3px solid #28a745 !important;
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
            transform: translateY(-5px) scale(1.02) !important;
        }

        .vehicle-card.selected::before {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%) !important;
            transform: scaleX(1) !important;
            height: 6px !important;
        }

        .vehicle-card.selected .vehicle-title {
            font-weight: 700 !important;
            color: #155724 !important;
            font-size: 1.15rem !important;
        }

        .vehicle-card.selected .vehicle-meta {
            font-weight: 600 !important;
            color: #2d5a3d !important;
        }

        .vehicle-card.selected .vehicle-meta strong {
            color: #155724 !important;
        }

        .vehicle-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .vehicle-details {
            flex: 1;
        }

        .vehicle-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.3;
            transition: all 0.3s ease;
        }

        .vehicle-meta {
            font-size: 0.85rem;
            color: #6c757d;
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .vehicle-meta strong {
            color: #495057;
            font-weight: 600;
        }

        .vehicle-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;
        }

        .brand-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .vehicle-card.selected .brand-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .selection-indicator {
            color: #28a745;
            font-size: 1.5rem;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .vehicle-card.selected .selection-indicator {
            opacity: 1;
            transform: scale(1);
        }

        /* Ensure styles are applied with higher specificity */
        .card-body .vehicle-card,
        div[class*="vehicle-card"] {
            border: 1px solid #e3e6f0 !important;
            border-radius: 12px !important;
            padding: 20px !important;
            margin-bottom: 15px !important;
            cursor: pointer !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
            position: relative !important;
            overflow: hidden !important;
            min-height: 120px !important;
        }

        .card-body .vehicle-card:hover,
        div[class*="vehicle-card"]:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            border-color: #667eea !important;
        }

        .card-body .vehicle-card.selected,
        div[class*="vehicle-card"].selected {
            border: 3px solid #28a745 !important;
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
            transform: translateY(-5px) scale(1.02) !important;
        }

        .card-body .vehicle-card.selected .vehicle-title,
        div[class*="vehicle-card"].selected .vehicle-title {
            font-weight: 700 !important;
            color: #155724 !important;
            font-size: 1.15rem !important;
        }

        .card-body .vehicle-card.selected .vehicle-meta,
        div[class*="vehicle-card"].selected .vehicle-meta {
            font-weight: 600 !important;
            color: #2d5a3d !important;
        }

        /* Test style to verify CSS is loading */
        .vehicle-card {
            background-color: #f0f8ff !important; /* Light blue background for testing */
        }

        /* Response Card Styling */
        .response-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
        }

        .response-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 25px;
            border: none;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        }

        .response-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .response-label {
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .response-status {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .response-controls .btn {
            border-color: rgba(255,255,255,0.3);
            color: white;
            margin-left: 2px;
        }

        .response-controls .btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            color: white;
        }

        .response-body {
            background: #f8f9fc;
            padding: 25px;
        }

        /* Response Metadata */
        .response-metadata {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e3e6f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .metadata-item {
            text-align: center;
        }

        .metadata-label {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .metadata-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        }

        .status-success {
            color: #27ae60 !important;
        }

        /* Object Counts Section */
        .object-counts-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e3e6f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .object-counts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .count-item {
            background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
            border: 1px solid #e3e6f0;
            border-radius: 6px;
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
        }

        .count-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
        }

        .count-path {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.8rem;
            color: #667eea;
            font-weight: 500;
        }

        .count-value {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* JSON Output Section */
        .json-output-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e3e6f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .json-output-container {
            background: #fafbfc;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            padding: 15px;
            max-height: 600px;
            overflow: auto;
        }

        .json-viewer-content {
            padding: 20px;
            background-color: #fafbfc;
            border-radius: 0 0 12px 12px;
        }

        /* Custom scrollbar for JSON viewer */
        .json-viewer-container::-webkit-scrollbar {
            width: 8px;
        }

        .json-viewer-container::-webkit-scrollbar-track {
            background: #f1f3f4;
            border-radius: 4px;
        }

        .json-viewer-container::-webkit-scrollbar-thumb {
            background: #c1c8cd;
            border-radius: 4px;
        }

        .json-viewer-container::-webkit-scrollbar-thumb:hover {
            background: #a8b2ba;
        }

        /* JSON Viewer Styles - Light Theme */
        .json-viewer-content.theme-light json-viewer,
        .json-viewer-content json-viewer {
            --background-color: #ffffff;
            --color: #2d3748;
            --font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
            --font-size: 0.9rem;
            --line-height: 1.6rem;
            --indent-size: 1.2em;
            --indentguide-size: 1px;
            --indentguide-style: solid;
            --indentguide-color: #e2e8f0;
            --indentguide-color-active: #cbd5e0;
            --outline-color: #667eea;
            --outline-width: 2px;
            --outline-style: solid;

            /* Light theme colors */
            --string-color: #38a169;
            --number-color: #3182ce;
            --boolean-color: #805ad5;
            --null-color: #a0aec0;
            --property-color: #d53f8c;
            --preview-color: #718096;
            --highlight-color: #ffd700;

            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 15px;
            background: var(--background-color);
        }

        /* Dark Theme */
        .json-viewer-content.theme-dark json-viewer {
            --background-color: #1a202c;
            --color: #e2e8f0;
            --indentguide-color: #4a5568;
            --indentguide-color-active: #718096;
            --outline-color: #90cdf4;

            /* Dark theme colors */
            --string-color: #68d391;
            --number-color: #63b3ed;
            --boolean-color: #b794f6;
            --null-color: #a0aec0;
            --property-color: #f687b3;
            --preview-color: #a0aec0;
            --highlight-color: #ffd700;

            border-color: #4a5568;
        }

        /* Auto theme detection */
        @media (prefers-color-scheme: dark) {
            .json-viewer-content.theme-auto json-viewer {
                --background-color: #1a202c;
                --color: #e2e8f0;
                --indentguide-color: #4a5568;
                --indentguide-color-active: #718096;
                --outline-color: #90cdf4;

                --string-color: #68d391;
                --number-color: #63b3ed;
                --boolean-color: #b794f6;
                --null-color: #a0aec0;
                --property-color: #f687b3;
                --preview-color: #a0aec0;
                --highlight-color: #ffd700;

                border-color: #4a5568;
            }
        }

        /* Enhanced styling */
        json-viewer {
            display: block;
            width: 100%;
            max-height: 600px;
            overflow: auto;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Custom scrollbar */
        json-viewer::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        json-viewer::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        json-viewer::-webkit-scrollbar-thumb {
            background: var(--indentguide-color-active);
            border-radius: 4px;
        }

        json-viewer::-webkit-scrollbar-thumb:hover {
            background: var(--outline-color);
        }

        /* Search controls styling */
        .json-search-container .form-control {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.3);
            color: white;
        }

        .json-search-container .form-control::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .json-search-container .form-control:focus {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        }

        /* External APIs Styling */
        .api-tabs-container .nav-pills .nav-link {
            background: #f8f9fc;
            border: 1px solid #e3e6f0;
            color: #5a5c69;
            margin-right: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .api-tabs-container .nav-pills .nav-link:hover {
            background: #e3e6f0;
            border-color: #d1d3e2;
            color: #3a3b45;
        }

        .api-tabs-container .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .api-details-card {
            background: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .params-list {
            background: white;
            border-radius: 6px;
            padding: 15px;
            border: 1px solid #e3e6f0;
        }

        .param-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .param-item:last-child {
            border-bottom: none;
        }

        .param-name {
            font-weight: 600;
            color: #2c3e50;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        }

        .param-mapping {
            color: #667eea;
            font-size: 0.85rem;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        }

        #run-api-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #run-api-btn:not(:disabled) {
            animation: pulse-success 2s infinite;
        }

        @keyframes pulse-success {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        /* Enhanced Vehicle Cards Styling */
        .vehicles-container {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border-radius: 16px;
            overflow: hidden;
        }

        .vehicles-header {
            background: #022b60;
            color: white;
            border: none;
            padding: 20px 25px;
        }

        .header-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .header-stats .badge-lg {
            font-size: 0.85rem;
            padding: 8px 12px;
            border-radius: 20px;
        }

        .vehicles-body {
            padding: 25px;
            background: #f8f9fc;
        }

        .document-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e3e6f0;
        }

        .document-header {
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }

        .document-title {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        .document-meta .badge {
            margin-left: 8px;
        }

        .vehicle-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .vehicle-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .vehicle-card-header {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
            border-bottom: 1px solid #e3e6f0;
        }

        .vehicle-name {
            color: #2c3e50;
            font-weight: 700;
            margin: 0;
            font-size: 1rem;
        }

        .vehicle-type {
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .brand-badge-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .vehicle-image img {
            max-height: 80px;
            width: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .vehicle-card-body {
            padding: 15px;
        }

        .detail-row-modern {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.2s ease;
        }

        .detail-row-modern:last-child {
            border-bottom: none;
        }

        .detail-row-modern:hover {
            background: rgba(102, 126, 234, 0.02);
            margin: 0 -15px;
            padding-left: 15px;
            padding-right: 15px;
            border-radius: 6px;
        }

        .detail-label-modern {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 0.85rem;
            min-width: 80px;
        }

        .detail-label-modern i {
            width: 16px;
            text-align: center;
        }

        .detail-value-modern {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: flex-end;
        }

        .value-code {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.8rem;
            color: #2c3e50;
            background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid #e3e6f0;
            font-weight: 500;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .copy-btn-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.7rem;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .copy-btn-modern:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6a4c93 100%);
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
        }

        .copy-btn-modern.copy-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            animation: pulse-success 0.6s ease-in-out;
        }

        .copy-btn-modern.copy-error {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        .vehicle-card-footer {
            padding: 12px 15px;
            background: #f8f9fc;
            border-top: 1px solid #e3e6f0;
        }

        .vehicle-card-footer small {
            font-size: 0.75rem;
        }

        @keyframes pulse-success {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .vehicles-body {
                padding: 15px;
            }

            .document-section {
                padding: 15px;
            }

            .vehicle-card-header,
            .vehicle-card-body {
                padding: 12px;
            }

            .value-code {
                max-width: 100px;
                font-size: 0.75rem;
            }
        }

        .accordion-toggle {
            background-color: transparent;
            border: none;
            border-radius: 12px 12px 0 0;
            transition: background 0.2s ease;
        }

        .accordion-toggle:hover {
            background-color: #f8f9fc;
        }

        .accordion-toggle:focus {
            outline: none;
            box-shadow: none;
        }

        .accordion-toggle.collapsed .toggle-icon {
            transform: rotate(0deg);
        }

        .accordion-toggle .toggle-icon {
            transition: transform 0.3s ease;
        }

        .accordion-toggle[aria-expanded="true"] .toggle-icon {
            transform: rotate(180deg);
        }

        /* Modern JSON Viewer Styles */
        .modern-json-viewer {
            border: none;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
        }

        .json-viewer-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 25px;
            border: none;
        }

        .json-viewer-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .viewer-label {
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .status-badge.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .status-badge.error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .json-viewer-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            transform: translateY(-1px);
        }

        .copy-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .copy-btn:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
        }

        .copy-btn.copied {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            animation: pulse-copy 0.6s ease;
        }

        @keyframes pulse-copy {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .metadata-bar {
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            padding: 15px 25px;
        }

        .metadata-items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .metadata-chip {
            display: flex;
            align-items: center;
            gap: 6px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            border: 1px solid #e3e6f0;
            font-size: 0.8rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .metadata-chip i {
            color: #6c757d;
            font-size: 0.75rem;
        }

        .metadata-chip .metadata-label {
            color: #6c757d;
            font-weight: 500;
        }

        .metadata-chip .metadata-value {
            color: #2c3e50;
            font-weight: 600;
        }

        .modern-json-container {
            padding: 25px;
            background: #ffffff;
        }

        .json-content {
            background: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            min-height: 300px;
            position: relative;
            overflow: hidden;
        }

        .json-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #6c757d;
            text-align: center;
        }

        .json-placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .json-placeholder p {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .json-placeholder small {
            opacity: 0.7;
        }

        .json-tree {
            padding: 20px;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            color: #2c3e50;
            background: white;
            border-radius: 12px;
            margin: 15px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .json-key {
            color: #8e44ad;
            font-weight: 600;
        }

        .json-string {
            color: #27ae60;
        }

        .json-number {
            color: #e67e22;
        }

        .json-boolean {
            color: #3498db;
            font-weight: 600;
        }

        .json-null {
            color: #95a5a6;
            font-style: italic;
        }

        .json-bracket {
            color: #34495e;
            font-weight: bold;
        }

        .json-expandable {
            cursor: pointer;
            user-select: none;
        }

        .json-expandable:hover {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 4px;
        }

        .json-collapsed {
            display: none;
        }

        .json-expand-icon {
            display: inline-block;
            width: 12px;
            text-align: center;
            margin-right: 5px;
            color: #7f8c8d;
            transition: transform 0.2s ease;
        }

        .json-expand-icon.expanded {
            transform: rotate(90deg);
        }

        .json-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #e74c3c;
            text-align: center;
        }

        .json-error i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .json-error p {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .json-error small {
            opacity: 0.8;
            max-width: 400px;
        }

        .json-minified {
            background: #f8f9fc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e3e6f0;
            word-break: break-all;
            font-size: 0.85rem;
            line-height: 1.4;
            color: #2c3e50;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .json-viewer-header {
                padding: 15px 20px;
            }

            .json-viewer-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .json-viewer-actions {
                margin-top: 10px;
            }

            .metadata-items {
                flex-direction: column;
                gap: 8px;
            }

            .modern-json-container {
                padding: 15px;
            }

            .action-btn .btn-text {
                display: none;
            }
        }


        /* Bootstrap API Groups Styling */
        .api-group-card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .api-group-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .api-group-card .btn-link {
            text-decoration: none;
            color: inherit;
            border-radius: 12px 12px 0 0;
        }

        .api-group-card .btn-link:hover {
            text-decoration: none;
            background: #f8f9fc;
        }

        .api-group-card .btn-link:focus {
            box-shadow: none;
        }

        .api-group-info h6 {
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .api-endpoint-card {
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
        }

        .api-endpoint-card:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f3ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
        }

        .api-endpoint-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .api-endpoint-card.selected .card-title,
        .api-endpoint-card.selected .card-text {
            color: white;
        }

        .api-endpoint-card .card-title {
            font-size: 0.95rem;
            color: #2c3e50;
        }

        .api-endpoint-card .card-text {
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .endpoint-path-container {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            padding: 4px 8px;
            margin-top: 8px;
        }

        .api-endpoint-card.selected .endpoint-path-container {
            background: rgba(255, 255, 255, 0.2);
        }

        .endpoint-path {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.75rem;
            color: #495057;
        }

        .api-endpoint-card.selected .endpoint-path {
            color: rgba(255, 255, 255, 0.9);
        }

        .badge-method {
            font-size: 0.7rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .method-get {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .method-post {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
        }
        .method-put {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: black;
        }
        .method-delete {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
        }
        .method-patch {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }

        /* Primary JSON Viewer Enhancements */
        .response-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
        }

        .response-header {
            border-radius: 12px 12px 0 0;
        }

        .metadata-item {
            text-align: center;
            padding: 8px;
        }

        .metadata-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .metadata-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-success {
            color: #28a745;
        }

        .status-danger {
            color: #dc3545;
        }

        .status-warning {
            color: #ffc107;
        }

        .status-secondary {
            color: #6c757d;
        }

        .json-viewer-content {
            background: #f8f9fc;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e3e6f0;
        }

        .object-counts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .count-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e3e6f0;
            font-size: 0.8rem;
        }

        .count-path {
            color: #495057;
            font-weight: 500;
        }

        .count-value {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.75rem;
        }

        .endpoint-path {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.8rem;
        }

        /* API Details and Parameters Cards */
        .api-details-card .card-header,
        .api-params-card .card-header,
        .api-response-card .card-header {
            font-weight: 600;
        }

        .api-detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .api-detail-item:last-child {
            border-bottom: none;
        }

        .api-url {
            background: #f8f9fc;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            border: 1px solid #e3e6f0;
        }

        .api-param-input {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.85rem;
        }

        .response-meta {
            font-size: 0.85rem;
        }

        .response-url {
            background: #f8f9fc;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .json-response-container {
            background: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 6px;
            padding: 15px;
            max-height: 400px;
            overflow: auto;
        }

        .notification-toast {
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
{% endblock %}

{% block content %}
{# 1. Base Form Component #}
{% include 'debug_api/_form.html.twig' %}

{# 2. Vehicles Display Component #}
{% include 'debug_api/_vehicles.html.twig' %}

{# 3. External APIs Component #}
{% include 'debug_api/_apis.html.twig' %}

{# 4. JSON Viewer Component #}
{% include 'debug_api/_json_viewer.html.twig' %}

{% endblock %}

{% block script %}
{# Modern JSON Viewer - No external dependencies needed #}

{# Include JavaScript Components #}
{% include 'debug_api/_scripts.html.twig' %}
{% include 'debug_api/_api_functions.html.twig' %}

<script>
// Modern JSON Viewer - all functionality is now in separate components
// Global functions for compatibility with other files
window.renderJsonContent = renderJsonContent;
window.updateJsonStats = updateJsonStats;
window.currentJsonData = currentJsonData;
</script>
{% endblock %}