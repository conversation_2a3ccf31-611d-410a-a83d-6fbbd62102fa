{% extends '_layout/layout.html.twig' %}

{% block title %}Debug API Form{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Force CSS refresh - Version 2.0 */
        /* JSON Viewer Styles */
        .json-viewer {
            color: #000;
            padding-left: 20px;
        }

        .json-viewer ul {
            list-style-type: none;
            margin: 0;
            margin: 0 0 0 1px;
            border-left: 1px dotted #ccc;
            padding-left: 2em;
        }

        .json-viewer .type-string {
            color: #0B7500;
        }

        .json-viewer .type-date {
            color: #CB7500;
        }

        .json-viewer .type-boolean {
            color: #1A01CC;
            font-weight: bold;
        }

        .json-viewer .type-number {
            color: #1A01CC;
        }

        .json-viewer .type-null {
            color: #90A;
        }

        .json-viewer a.list-link {
            color: #000;
            text-decoration: none;
            position: relative;
        }

        .json-viewer a.list-link:before {
            color: #aaa;
            content: "▶";
            position: absolute;
            left: -1em;
        }

        .json-viewer a.list-link.collapsed:before {
            content: "▼";
        }

        .json-viewer a.list-link.empty:before {
            content: "";
        }

        .json-viewer .items-ph {
            color: #aaa;
            cursor: pointer;
        }

        .json-viewer .items-ph:hover {
            text-decoration: underline;
        }
        .vehicle-card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .vehicle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .vehicle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .vehicle-card:hover::before {
            transform: scaleX(1);
        }

        .vehicle-card.selected {
            border: 3px solid #28a745 !important;
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
            transform: translateY(-5px) scale(1.02) !important;
        }

        .vehicle-card.selected::before {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%) !important;
            transform: scaleX(1) !important;
            height: 6px !important;
        }

        .vehicle-card.selected .vehicle-title {
            font-weight: 700 !important;
            color: #155724 !important;
            font-size: 1.15rem !important;
        }

        .vehicle-card.selected .vehicle-meta {
            font-weight: 600 !important;
            color: #2d5a3d !important;
        }

        .vehicle-card.selected .vehicle-meta strong {
            color: #155724 !important;
        }

        .vehicle-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .vehicle-details {
            flex: 1;
        }

        .vehicle-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.3;
            transition: all 0.3s ease;
        }

        .vehicle-meta {
            font-size: 0.85rem;
            color: #6c757d;
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .vehicle-meta strong {
            color: #495057;
            font-weight: 600;
        }

        .vehicle-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;
        }

        .brand-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .vehicle-card.selected .brand-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .selection-indicator {
            color: #28a745;
            font-size: 1.5rem;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .vehicle-card.selected .selection-indicator {
            opacity: 1;
            transform: scale(1);
        }

        /* Ensure styles are applied with higher specificity */
        .card-body .vehicle-card,
        div[class*="vehicle-card"] {
            border: 1px solid #e3e6f0 !important;
            border-radius: 12px !important;
            padding: 20px !important;
            margin-bottom: 15px !important;
            cursor: pointer !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
            position: relative !important;
            overflow: hidden !important;
            min-height: 120px !important;
        }

        .card-body .vehicle-card:hover,
        div[class*="vehicle-card"]:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            border-color: #667eea !important;
        }

        .card-body .vehicle-card.selected,
        div[class*="vehicle-card"].selected {
            border: 3px solid #28a745 !important;
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
            transform: translateY(-5px) scale(1.02) !important;
        }

        .card-body .vehicle-card.selected .vehicle-title,
        div[class*="vehicle-card"].selected .vehicle-title {
            font-weight: 700 !important;
            color: #155724 !important;
            font-size: 1.15rem !important;
        }

        .card-body .vehicle-card.selected .vehicle-meta,
        div[class*="vehicle-card"].selected .vehicle-meta {
            font-weight: 600 !important;
            color: #2d5a3d !important;
        }

        /* Test style to verify CSS is loading */
        .vehicle-card {
            background-color: #f0f8ff !important; /* Light blue background for testing */
        }

        /* Response Card Styling */
        .response-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
        }

        .response-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 25px;
            border: none;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        }

        .response-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .response-label {
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .response-status {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .response-controls .btn {
            border-color: rgba(255,255,255,0.3);
            color: white;
            margin-left: 2px;
        }

        .response-controls .btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            color: white;
        }

        .response-body {
            background: #f8f9fc;
            padding: 25px;
        }

        /* Response Metadata */
        .response-metadata {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e3e6f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .metadata-item {
            text-align: center;
        }

        .metadata-label {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .metadata-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        }

        .status-success {
            color: #27ae60 !important;
        }

        /* Object Counts Section */
        .object-counts-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e3e6f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .object-counts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .count-item {
            background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
            border: 1px solid #e3e6f0;
            border-radius: 6px;
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
        }

        .count-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
        }

        .count-path {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 0.8rem;
            color: #667eea;
            font-weight: 500;
        }

        .count-value {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* JSON Output Section */
        .json-output-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e3e6f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .json-output-container {
            background: #fafbfc;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            padding: 15px;
            max-height: 600px;
            overflow: auto;
        }

        .json-viewer-content {
            padding: 20px;
            background-color: #fafbfc;
            border-radius: 0 0 12px 12px;
        }

        /* Custom scrollbar for JSON viewer */
        .json-viewer-container::-webkit-scrollbar {
            width: 8px;
        }

        .json-viewer-container::-webkit-scrollbar-track {
            background: #f1f3f4;
            border-radius: 4px;
        }

        .json-viewer-container::-webkit-scrollbar-thumb {
            background: #c1c8cd;
            border-radius: 4px;
        }

        .json-viewer-container::-webkit-scrollbar-thumb:hover {
            background: #a8b2ba;
        }

        /* JSON Viewer Styles - Light Theme */
        .json-viewer-content.theme-light json-viewer,
        .json-viewer-content json-viewer {
            --background-color: #ffffff;
            --color: #2d3748;
            --font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
            --font-size: 0.9rem;
            --line-height: 1.6rem;
            --indent-size: 1.2em;
            --indentguide-size: 1px;
            --indentguide-style: solid;
            --indentguide-color: #e2e8f0;
            --indentguide-color-active: #cbd5e0;
            --outline-color: #667eea;
            --outline-width: 2px;
            --outline-style: solid;

            /* Light theme colors */
            --string-color: #38a169;
            --number-color: #3182ce;
            --boolean-color: #805ad5;
            --null-color: #a0aec0;
            --property-color: #d53f8c;
            --preview-color: #718096;
            --highlight-color: #ffd700;

            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 15px;
            background: var(--background-color);
        }

        /* Dark Theme */
        .json-viewer-content.theme-dark json-viewer {
            --background-color: #1a202c;
            --color: #e2e8f0;
            --indentguide-color: #4a5568;
            --indentguide-color-active: #718096;
            --outline-color: #90cdf4;

            /* Dark theme colors */
            --string-color: #68d391;
            --number-color: #63b3ed;
            --boolean-color: #b794f6;
            --null-color: #a0aec0;
            --property-color: #f687b3;
            --preview-color: #a0aec0;
            --highlight-color: #ffd700;

            border-color: #4a5568;
        }

        /* Auto theme detection */
        @media (prefers-color-scheme: dark) {
            .json-viewer-content.theme-auto json-viewer {
                --background-color: #1a202c;
                --color: #e2e8f0;
                --indentguide-color: #4a5568;
                --indentguide-color-active: #718096;
                --outline-color: #90cdf4;

                --string-color: #68d391;
                --number-color: #63b3ed;
                --boolean-color: #b794f6;
                --null-color: #a0aec0;
                --property-color: #f687b3;
                --preview-color: #a0aec0;
                --highlight-color: #ffd700;

                border-color: #4a5568;
            }
        }

        /* Enhanced styling */
        json-viewer {
            display: block;
            width: 100%;
            max-height: 600px;
            overflow: auto;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Custom scrollbar */
        json-viewer::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        json-viewer::-webkit-scrollbar-track {
            background: var(--background-color);
        }

        json-viewer::-webkit-scrollbar-thumb {
            background: var(--indentguide-color-active);
            border-radius: 4px;
        }

        json-viewer::-webkit-scrollbar-thumb:hover {
            background: var(--outline-color);
        }

        /* Search controls styling */
        .json-search-container .form-control {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.3);
            color: white;
        }

        .json-search-container .form-control::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .json-search-container .form-control:focus {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        }
    </style>
{% endblock %}

{% block content %}
<div class="card">
    {% include '_layout/form_card_header.html.twig' with{
        'title': 'Debug API Form',
    }%}
    
    <div class="card-body">
        {{ form_start(form, {'attr': {'id': 'debug-api-form', 'novalidate': 'novalidate'}}) }}
        
        <div class="row">
            <!-- Optional Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Search Filters (At least one required)</h5>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Please provide at least one filter to search for vehicles. You can combine multiple filters for more specific results.
                </div>
            </div>
            
            <!-- Email Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.email.vars.id }}">{{ form.email.vars.label }}</label>
                    {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Enter email address'}}) }}
                    {{ form_errors(form.email) }}
                </div>
            </div>
            
            <!-- User ID Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.userID.vars.id }}">{{ form.userID.vars.label }}</label>
                    {{ form_widget(form.userID, {'attr': {'class': 'form-control', 'placeholder': 'Enter user ID'}}) }}
                    {{ form_errors(form.userID) }}
                </div>
            </div>
            
            <!-- VIN Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.vin.vars.id }}">{{ form.vin.vars.label }}</label>
                    {{ form_widget(form.vin, {'attr': {'class': 'form-control', 'placeholder': 'Enter VIN'}}) }}
                    {{ form_errors(form.vin) }}
                </div>
            </div>

            <hr class="my-4">

            <!-- Country Filter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.country.vars.id }}">{{ form.country.vars.label }}</label>
                    {{ form_widget(form.country, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.country) }}
                    {% if form.country.vars.help %}
                        <small class="form-text text-muted">{{ form.country.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

             <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.language.vars.id }}">{{ form.language.vars.label }}</label>
                    {{ form_widget(form.language, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.language) }}
                    {% if form.language.vars.help %}
                        <small class="form-text text-muted">{{ form.language.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

            <!-- Source Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.source.vars.id }}">{{ form.source.vars.label }}</label>
                    {{ form_widget(form.source, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.source) }}
                    {% if form.source.vars.help %}
                        <small class="form-text text-muted">{{ form.source.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

            <!-- Target Parameter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.targetParam.vars.id }}">{{ form.targetParam.vars.label }}</label>
                    {{ form_widget(form.targetParam, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.targetParam) }}
                    {% if form.targetParam.vars.help %}
                        <small class="form-text text-muted">{{ form.targetParam.vars.help }}</small>
                    {% endif %}
                </div>
            </div>
        </div>

        <hr class="my-4">
        
        <!-- Submit Button -->
        <div class="card-footer d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i>
                Submit
            </button>
        </div>
        
        {{ form_end(form) }}
    </div>
</div>

{% if error %}
<div class="card mt-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Error</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <strong>Error Code:</strong> {{ error.code }}<br>
            <strong>Message:</strong> {{ error.message }}
        </div>
    </div>
</div>
{% endif %}

{% if vehicles is not empty %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border-radius: 12px;">
    <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px 12px 0 0; border: none;">
        <h5 class="mb-0"><i class="fas fa-car mr-2"></i>Vehicles Found ({{ vehicles|length }})</h5>
        <small style="color: rgba(255,255,255,0.8);">Click on a vehicle to select it (single selection)</small>
    </div>
    <div class="card-body" style="padding: 25px;">
        <div class="row">
            {% for vehicle in vehicles %}
            <div class="col-md-6 col-lg-4">
                <div class="vehicle-card" data-vin="{{ vehicle.vin }}" data-user-id="{{ vehicle.userId }}">
                    <div class="vehicle-info">
                        <div class="vehicle-details">
                            <div class="vehicle-title">{{ vehicle.label }}</div>
                            <div class="vehicle-meta">
                                <div><strong>VIN:</strong> {{ vehicle.vin }}</div>
                                <div><strong>Version ID:</strong> {{ vehicle.versionId }}</div>
                                <div><strong>Owner:</strong> {{ vehicle.userName }} ({{ vehicle.userEmail }})</div>
                                <div><strong>Country:</strong> {{ vehicle.userCountry }}</div>
                                {% if vehicle.mileage != 'N/A' %}
                                <div><strong>Mileage:</strong> {{ vehicle.mileage }} {{ vehicle.mileageUnit }}</div>
                                {% endif %}
                                {% if vehicle.featureCodesCount > 0 %}
                                <div><strong>Features:</strong> {{ vehicle.featureCodesCount }} configured</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="vehicle-actions">
                            <div class="selection-indicator">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <span class="brand-badge">{{ vehicle.brand }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>


    </div>
</div>
{% endif %}

{% if jsonData %}
<div class="card mt-4 response-card">
    <div class="card-header response-header">
        <div class="d-flex justify-content-between align-items-center">
            <div class="response-title">
                <i class="fas fa-server mr-2"></i>
                <span class="response-label">Response</span>
                <span class="response-status">200 OK</span>
                <button type="button" class="btn btn-dark btn-sm ml-3" id="copy-json-data" title="Copy JSON Data" style="background-color: #343a40; border-color: #343a40; color: white;">
                    <i class="fas fa-copy mr-1"></i>Copy JSON
                </button>
            </div>
        </div>
    </div>

    <div class="card-body response-body">
        <!-- JSON Viewer -->
        <div class="json-output-section">
            <h6 class="section-title">
                <i class="fas fa-code mr-2"></i>JSON Response Body
            </h6>
            <div class="json-output-container">
                <json-viewer id="json-renderer"></json-viewer>
            </div>
        </div>
        <!-- Response Metadata -->
        <div class="response-metadata mb-4">
            <div class="row">
                <div class="col-md-2">
                    <div class="metadata-item">
                        <div class="metadata-label">Status</div>
                        <div class="metadata-value status-success">
                            <i class="fas fa-check-circle mr-1"></i>200 OK
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metadata-item">
                        <div class="metadata-label">Content-Type</div>
                        <div class="metadata-value">application/json</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metadata-item">
                        <div class="metadata-label">Size</div>
                        <div class="metadata-value" id="response-size">-</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metadata-item">
                        <div class="metadata-label">Documents</div>
                        <div class="metadata-value" id="doc-count">-</div>
                    </div>
                </div>
            </div>
        </div>

        
    </div>
</div>
{% endif %}

{% endblock %}

{% block script %}
<script src="https://unpkg.com/@alenaksu/json-viewer@2.1.0/dist/json-viewer.bundle.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Vehicle selection functionality (single selection only)
    let selectedVehicle = null;

    // Debug: Check if vehicle cards exist
    console.log('Vehicle cards found:', $('.vehicle-card').length);

    // Initialize vehicle cards styling
    function initializeVehicleCards() {
        $('.vehicle-card').each(function() {
            $(this).addClass('vehicle-card-initialized');
            // Force apply styles
            $(this).css({
                'border': '1px solid #e3e6f0',
                'border-radius': '12px',
                'padding': '20px',
                'margin-bottom': '15px',
                'cursor': 'pointer',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%)',
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)',
                'position': 'relative',
                'overflow': 'hidden',
                'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            });
        });
        console.log('Vehicle cards initialized with forced styles:', $('.vehicle-card').length);
    }

    // Initialize on page load
    initializeVehicleCards();

    // Use event delegation to handle dynamically loaded content
    $(document).on('click', '.vehicle-card', function() {
        const vin = $(this).data('vin');
        const userId = $(this).data('user-id');
        const label = $(this).find('.vehicle-title').text();

        if ($(this).hasClass('selected')) {
            // Deselect current vehicle
            $(this).removeClass('selected');
            // Reset styles to default
            $(this).css({
                'border': '1px solid #e3e6f0',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%)',
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)',
                'transform': 'translateY(0) scale(1)'
            });
            selectedVehicle = null;
        } else {
            // Deselect all other vehicles first
            $('.vehicle-card').removeClass('selected');
            $('.vehicle-card').css({
                'border': '1px solid #e3e6f0',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%)',
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)',
                'transform': 'translateY(0) scale(1)'
            });

            // Select this vehicle
            $(this).addClass('selected');
            // Apply selected styles
            $(this).css({
                'border': '3px solid #28a745',
                'background': 'linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%)',
                'box-shadow': '0 12px 40px rgba(40, 167, 69, 0.4)',
                'transform': 'translateY(-5px) scale(1.02)'
            });

            // Style the title and meta for selected card
            $(this).find('.vehicle-title').css({
                'font-weight': '700',
                'color': '#155724',
                'font-size': '1.15rem'
            });

            $(this).find('.vehicle-meta').css({
                'font-weight': '600',
                'color': '#2d5a3d'
            });

            selectedVehicle = {
                vin: vin,
                userId: userId,
                label: label
            };
        }

        // Log selected vehicle to console for debugging (optional)
        console.log('Selected vehicle:', selectedVehicle);
    });

    // Enhanced JSON Viewer with @alenaksu/json-viewer
    {% if jsonData %}
    let jsonData = null;
    let jsonViewer = null;
    let currentTheme = 'auto';
    let searchIterator = null;

    try {
        // Parse JSON from Twig variable
        jsonData = JSON.parse(`{{ jsonData|e('js') }}`);

        // Initialize JSON viewer
        initializeJsonViewer();

        // Calculate and display statistics
        updateJsonStats(jsonData);

        console.log('Enhanced JSON Viewer initialized successfully');
    } catch (error) {
        console.error('Error initializing JSON viewer:', error);
        document.querySelector("#json-renderer").innerHTML = '<div class="alert alert-danger">Error displaying JSON data: ' + error.message + '</div>';
    }

    function initializeJsonViewer() {
        // Get the json-viewer element
        jsonViewer = document.querySelector("#json-renderer");

        // Set the data
        jsonViewer.data = jsonData;

        // Setup event listeners
        setupJsonViewerControls();
    }

    function updateJsonStats(data) {
        // Count documents
        const docCount = data.documents ? data.documents.length : 0;
        document.getElementById('doc-count').textContent = docCount;

        // Count vehicles
        let vehicleCount = 0;
        if (data.documents) {
            data.documents.forEach(doc => {
                if (doc.vehicle && Array.isArray(doc.vehicle)) {
                    vehicleCount += doc.vehicle.length;
                }
            });
        }
        document.getElementById('vehicle-count').textContent = vehicleCount;

        // Calculate size
        const jsonString = JSON.stringify(data);
        const sizeKB = (jsonString.length / 1024).toFixed(2);
        document.getElementById('response-size').textContent = sizeKB + ' KB';

        // Count total objects
        const objectCounts = countObjectsAtEachLevel(data);
        const totalObjects = Object.values(objectCounts).reduce((sum, count) => sum + count, 0);
        document.getElementById('total-objects').textContent = totalObjects;

        // Display object counts breakdown
        displayObjectCounts(objectCounts);
    }

    function countObjectsAtEachLevel(obj, path = '', counts = {}) {
        if (typeof obj !== 'object' || obj === null) {
            return counts;
        }

        if (Array.isArray(obj)) {
            // Count array items
            const arrayPath = path || 'root[]';
            counts[arrayPath] = obj.length;

            // Count objects within array
            obj.forEach((item, index) => {
                if (typeof item === 'object' && item !== null) {
                    const itemPath = path ? `${path}[${index}]` : `root[${index}]`;
                    countObjectsAtEachLevel(item, itemPath, counts);
                }
            });
        } else {
            // Count object properties
            const keys = Object.keys(obj);
            const objectPath = path || 'root';
            counts[objectPath] = keys.length;

            // Recursively count nested objects
            keys.forEach(key => {
                const value = obj[key];
                if (typeof value === 'object' && value !== null) {
                    const nestedPath = path ? `${path}.${key}` : key;
                    countObjectsAtEachLevel(value, nestedPath, counts);
                }
            });
        }

        return counts;
    }

    function displayObjectCounts(counts) {
        const container = document.getElementById('object-counts');
        container.innerHTML = '';

        // Sort paths by depth and name
        const sortedPaths = Object.keys(counts).sort((a, b) => {
            const depthA = (a.match(/\./g) || []).length;
            const depthB = (b.match(/\./g) || []).length;
            if (depthA !== depthB) return depthA - depthB;
            return a.localeCompare(b);
        });

        sortedPaths.forEach(path => {
            const count = counts[path];
            const countItem = document.createElement('div');
            countItem.className = 'count-item';

            // Determine the type and icon
            let icon = 'fas fa-cube';
            let pathDisplay = path;

            if (path.includes('[]')) {
                icon = 'fas fa-list';
                pathDisplay = path.replace('[]', ' (array)');
            } else if (path.includes('[') && path.includes(']')) {
                icon = 'fas fa-layer-group';
            }

            countItem.innerHTML = `
                <div class="count-path">
                    <i class="${icon} mr-1"></i>
                    ${pathDisplay}
                </div>
                <div class="count-value">${count}</div>
            `;

            container.appendChild(countItem);
        });
    }

    function getObjectDepth(obj) {
        if (typeof obj !== 'object' || obj === null) return 0;
        let maxDepth = 0;
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                const depth = getObjectDepth(obj[key]);
                maxDepth = Math.max(maxDepth, depth);
            }
        }
        return maxDepth + 1;
    }

    function setupJsonViewerControls() {
        // Expand all
        document.getElementById('expand-all').addEventListener('click', function() {
            if (jsonViewer) {
                jsonViewer.expand('**');
            }
        });

        // Collapse all
        document.getElementById('collapse-all').addEventListener('click', function() {
            if (jsonViewer) {
                jsonViewer.collapse('**');
            }
        });

        // Copy JSON
        document.getElementById('copy-json').addEventListener('click', function() {
            const jsonString = JSON.stringify(jsonData, null, 2);
            navigator.clipboard.writeText(jsonString).then(() => {
                // Show success feedback
                const btn = this;
                const originalIcon = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-outline-light');

                setTimeout(() => {
                    btn.innerHTML = originalIcon;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-light');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy JSON:', err);
            });
        });

        // Theme switcher
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const theme = this.dataset.theme;
                setTheme(theme);

                // Update active state
                document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Display options
        document.getElementById('show-counts').addEventListener('change', function() {
            const countsSection = document.querySelector('.object-counts-section');
            if (this.checked) {
                countsSection.style.display = 'block';
            } else {
                countsSection.style.display = 'none';
            }
        });

        document.getElementById('show-types').addEventListener('change', function() {
            // The new json-viewer shows types by default
            console.log('Type display toggled:', this.checked);
        });

        document.getElementById('word-wrap').addEventListener('change', function() {
            if (jsonViewer) {
                if (this.checked) {
                    jsonViewer.style.whiteSpace = 'pre-wrap';
                    jsonViewer.style.wordBreak = 'break-word';
                } else {
                    jsonViewer.style.whiteSpace = 'pre';
                    jsonViewer.style.wordBreak = 'normal';
                }
            }
        });
    }

    function setTheme(theme) {
        currentTheme = theme;
        const content = document.querySelector('.json-viewer-content');

        // Remove existing theme classes
        content.classList.remove('theme-light', 'theme-dark', 'theme-auto');

        if (theme === 'dark') {
            content.classList.add('theme-dark');
        } else if (theme === 'light') {
            content.classList.add('theme-light');
        } else {
            // Auto theme - uses CSS media queries
            content.classList.add('theme-auto');
        }
    }

    // Initialize with auto theme
    setTheme('auto');
    {% endif %}

    // Copy JSON Data button functionality (works independently)
    {% if jsonData %}
    const copyButton = document.getElementById('copy-json-data');
    if (copyButton) {
        copyButton.addEventListener('click', function() {
            try {
                // Parse the JSON data from Twig
                const jsonDataToCopy = JSON.parse(`{{ jsonData|e('js') }}`);
                const jsonString = JSON.stringify(jsonDataToCopy, null, 2);

                navigator.clipboard.writeText(jsonString).then(() => {
                    // Show success feedback
                    const btn = this;
                    const originalContent = btn.innerHTML;
                    const originalStyle = btn.getAttribute('style');

                    btn.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                    btn.classList.remove('btn-dark');
                    btn.classList.add('btn-success');
                    btn.style.cssText = 'background-color: #28a745; border-color: #28a745; color: white;';

                    setTimeout(() => {
                        btn.innerHTML = originalContent;
                        btn.classList.remove('btn-success');
                        btn.classList.add('btn-dark');
                        btn.setAttribute('style', originalStyle);
                    }, 2000);
                }).catch(err => {
                    console.error('Failed to copy JSON:', err);
                    // Show error feedback
                    const btn = this;
                    const originalContent = btn.innerHTML;
                    const originalStyle = btn.getAttribute('style');

                    btn.innerHTML = '<i class="fas fa-times mr-1"></i>Failed';
                    btn.classList.remove('btn-dark');
                    btn.classList.add('btn-danger');
                    btn.style.cssText = 'background-color: #dc3545; border-color: #dc3545; color: white;';

                    setTimeout(() => {
                        btn.innerHTML = originalContent;
                        btn.classList.remove('btn-danger');
                        btn.classList.add('btn-dark');
                        btn.setAttribute('style', originalStyle);
                    }, 2000);
                });
            } catch (error) {
                console.error('Error parsing JSON data:', error);
                alert('Error: Could not copy JSON data');
            }
        });
    }
    {% endif %}

    // Form validation
    $('#debug-api-form').on('submit', function(e) {
        var email = $('#{{ form.email.vars.id }}').val();
        var userID = $('#{{ form.userID.vars.id }}').val();
        var vin = $('#{{ form.vin.vars.id }}').val();
        var country = $('#{{ form.country.vars.id }}').val();

        // Check if at least one filter field is filled
        if (!email && !userID && !vin && !country) {
            e.preventDefault();

            // Show error message
            if ($('.identification-error').length === 0) {
                $('.alert-info').after(
                    '<div class="alert alert-danger identification-error">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Please provide at least one filter (Email, User ID, VIN, or Country).' +
                    '</div>'
                );
            }

            // Scroll to top to show error
            $('html, body').animate({
                scrollTop: $('.identification-error').offset().top - 100
            }, 500);

            return false;
        }

        // Remove any existing error messages
        $('.identification-error').remove();
    });

    // Remove error message when user starts typing in any filter field
    $('#{{ form.email.vars.id }}, #{{ form.userID.vars.id }}, #{{ form.vin.vars.id }}, #{{ form.country.vars.id }}').on('input change', function() {
        $('.identification-error').remove();
    });
});
</script>
{% endblock %}