{% extends '_layout/layout.html.twig' %}

{% block title %}Debug API Form{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* JSON Viewer Styles */
        .json-viewer {
            color: #000;
            padding-left: 20px;
        }

        .json-viewer ul {
            list-style-type: none;
            margin: 0;
            margin: 0 0 0 1px;
            border-left: 1px dotted #ccc;
            padding-left: 2em;
        }

        .json-viewer .type-string {
            color: #0B7500;
        }

        .json-viewer .type-date {
            color: #CB7500;
        }

        .json-viewer .type-boolean {
            color: #1A01CC;
            font-weight: bold;
        }

        .json-viewer .type-number {
            color: #1A01CC;
        }

        .json-viewer .type-null {
            color: #90A;
        }

        .json-viewer a.list-link {
            color: #000;
            text-decoration: none;
            position: relative;
        }

        .json-viewer a.list-link:before {
            color: #aaa;
            content: "▶";
            position: absolute;
            left: -1em;
        }

        .json-viewer a.list-link.collapsed:before {
            content: "▼";
        }

        .json-viewer a.list-link.empty:before {
            content: "";
        }

        .json-viewer .items-ph {
            color: #aaa;
            cursor: pointer;
        }

        .json-viewer .items-ph:hover {
            text-decoration: underline;
        }
        .vehicle-card {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        .vehicle-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
        }

        .vehicle-card.selected {
            border-color: #28a745;
            background-color: #f8fff9;
            box-shadow: 0 4px 8px rgba(40,167,69,0.3);
        }

        .vehicle-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .vehicle-details h6 {
            margin-bottom: 5px;
            color: #495057;
        }

        .vehicle-meta {
            font-size: 0.9em;
            color: #6c757d;
        }

        .brand-badge {
            background-color: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8em;
        }

        .selection-indicator {
            color: #28a745;
            font-size: 1.2em;
            display: none;
        }

        .vehicle-card.selected .selection-indicator {
            display: block;
        }

        .json-viewer-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        /* Renderjson Styles */
        .renderjson {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }

        .renderjson a {
            text-decoration: none;
            color: #007bff;
        }

        .renderjson a:hover {
            text-decoration: underline;
        }

        .renderjson .disclosure {
            color: #007bff;
            font-weight: bold;
            margin-right: 5px;
        }

        .renderjson .syntax {
            color: #666;
        }

        .renderjson .string {
            color: #0B7500;
        }

        .renderjson .number {
            color: #1A01CC;
        }

        .renderjson .boolean {
            color: #1A01CC;
            font-weight: bold;
        }

        .renderjson .key {
            color: #881391;
            font-weight: bold;
        }

        .renderjson .keyword {
            color: #90A;
        }

        .renderjson .object.syntax {
            color: #000;
        }

        .renderjson .array.syntax {
            color: #000;
        }
    </style>
{% endblock %}

{% block content %}
<div class="card">
    {% include '_layout/form_card_header.html.twig' with{
        'title': 'Debug API Form',
    }%}
    
    <div class="card-body">
        {{ form_start(form, {'attr': {'id': 'debug-api-form', 'novalidate': 'novalidate'}}) }}
        
        <div class="row">
            <!-- Optional Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Identification (At least one required)</h5>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Please provide at least one of the following identification fields:
                </div>
            </div>
            
            <!-- Email Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.email.vars.id }}">{{ form.email.vars.label }}</label>
                    {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Enter email address'}}) }}
                    {{ form_errors(form.email) }}
                </div>
            </div>
            
            <!-- User ID Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.userID.vars.id }}">{{ form.userID.vars.label }}</label>
                    {{ form_widget(form.userID, {'attr': {'class': 'form-control', 'placeholder': 'Enter user ID'}}) }}
                    {{ form_errors(form.userID) }}
                </div>
            </div>
            
            <!-- VIN Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.vin.vars.id }}">{{ form.vin.vars.label }}</label>
                    {{ form_widget(form.vin, {'attr': {'class': 'form-control', 'placeholder': 'Enter VIN'}}) }}
                    {{ form_errors(form.vin) }}
                </div>
            </div>
        </div>
        
        <hr class="my-4">
        
        <div class="row">
            <!-- Required Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Required Information</h5>
            </div>
            
            <!-- Country Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.country.vars.id }}">
                        {{ form.country.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.country, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.country) }}
                </div>
            </div>
            
            <!-- Language Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.language.vars.id }}">
                        {{ form.language.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.language, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.language) }}
                </div>
            </div>
            
            <!-- Source Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.source.vars.id }}">
                        {{ form.source.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.source, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.source) }}
                </div>
            </div>
            
            <!-- Target Parameter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.targetParam.vars.id }}">
                        {{ form.targetParam.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.targetParam, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.targetParam) }}
                </div>
            </div>
        </div>
        
        <!-- Submit Button -->
        <div class="card-footer d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i>
                Submit
            </button>
        </div>
        
        {{ form_end(form) }}
    </div>
</div>

{% if error %}
<div class="card mt-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Error</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <strong>Error Code:</strong> {{ error.code }}<br>
            <strong>Message:</strong> {{ error.message }}
        </div>
    </div>
</div>
{% endif %}

{% if vehicles is not empty %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-car"></i> Vehicles Found ({{ vehicles|length }})</h5>
        <small class="text-muted">Click on vehicles to select them</small>
    </div>
    <div class="card-body">
        <div class="row">
            {% for vehicle in vehicles %}
            <div class="col-md-6 col-lg-4">
                <div class="vehicle-card" data-vin="{{ vehicle.vin }}" data-user-id="{{ vehicle.userId }}">
                    <div class="vehicle-info">
                        <div class="vehicle-details">
                            <h6>{{ vehicle.label }}</h6>
                            <div class="vehicle-meta">
                                <strong>VIN:</strong> {{ vehicle.vin }}<br>
                                <strong>Version ID:</strong> {{ vehicle.versionId }}<br>
                                <strong>User ID:</strong> {{ vehicle.userId }}
                            </div>
                        </div>
                        <div class="vehicle-actions">
                            <span class="brand-badge">{{ vehicle.brand }}</span>
                            <div class="selection-indicator">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="mt-3">
            <div class="alert alert-info" id="selection-info" style="display: none;">
                <strong>Selected Vehicles:</strong> <span id="selected-count">0</span>
                <div id="selected-list" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if jsonData %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-code"></i> MongoDB Data (JSON Viewer)</h5>
    </div>
    <div class="card-body">
        <div class="json-viewer-container">
            <div id="json-renderer"></div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block script %}
<script src="https://cdn.jsdelivr.net/gh/caldwell/renderjson/renderjson.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Vehicle selection functionality
    let selectedVehicles = [];

    $('.vehicle-card').on('click', function() {
        const vin = $(this).data('vin');
        const userId = $(this).data('user-id');
        const label = $(this).find('h6').text();

        if ($(this).hasClass('selected')) {
            // Deselect vehicle
            $(this).removeClass('selected');
            selectedVehicles = selectedVehicles.filter(v => v.vin !== vin);
        } else {
            // Select vehicle
            $(this).addClass('selected');
            selectedVehicles.push({
                vin: vin,
                userId: userId,
                label: label
            });
        }

        updateSelectionInfo();
    });

    function updateSelectionInfo() {
        const count = selectedVehicles.length;
        $('#selected-count').text(count);

        if (count > 0) {
            $('#selection-info').show();
            let listHtml = '';
            selectedVehicles.forEach(vehicle => {
                listHtml += `<span class="badge badge-success mr-2 mb-1">
                    ${vehicle.label} (${vehicle.vin})
                </span>`;
            });
            $('#selected-list').html(listHtml);
        } else {
            $('#selection-info').hide();
        }

        // Log selected vehicles to console for debugging
        console.log('Selected vehicles:', selectedVehicles);
    }

    // Initialize JSON Viewer
    {% if jsonData %}
    try {
        // Parse JSON from Twig variable
        const data = JSON.parse(`{{ jsonData|e('js') }}`);

        // Configure renderjson
        renderjson.set_icons('+', '-');
        renderjson.set_show_to_level(2); // Show 2 levels by default
        renderjson.set_max_string_length(100); // Limit string length

        // Render JSON
        const jsonElement = renderjson(data);
        document.querySelector("#json-renderer").appendChild(jsonElement);

        console.log('JSON Viewer initialized successfully');
    } catch (error) {
        console.error('Error initializing JSON viewer:', error);
        document.querySelector("#json-renderer").innerHTML = '<div class="alert alert-danger">Error displaying JSON data: ' + error.message + '</div>';
    }
    {% endif %}

    // Form validation
    $('#debug-api-form').on('submit', function(e) {
        var email = $('#{{ form.email.vars.id }}').val();
        var userID = $('#{{ form.userID.vars.id }}').val();
        var vin = $('#{{ form.vin.vars.id }}').val();

        // Check if at least one identification field is filled
        if (!email && !userID && !vin) {
            e.preventDefault();

            // Show error message
            if ($('.identification-error').length === 0) {
                $('.alert-info').after(
                    '<div class="alert alert-danger identification-error">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Please provide at least one identification field (Email, User ID, or VIN).' +
                    '</div>'
                );
            }

            // Scroll to top to show error
            $('html, body').animate({
                scrollTop: $('.identification-error').offset().top - 100
            }, 500);

            return false;
        }

        // Remove any existing error messages
        $('.identification-error').remove();
    });

    // Remove error message when user starts typing in any identification field
    $('#{{ form.email.vars.id }}, #{{ form.userID.vars.id }}, #{{ form.vin.vars.id }}').on('input', function() {
        $('.identification-error').remove();
    });
});
</script>
{% endblock %}