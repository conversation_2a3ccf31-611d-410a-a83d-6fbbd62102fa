{% extends '_layout/layout.html.twig' %}

{% block title %}Debug API Form{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Force CSS refresh - Version 2.0 */
        /* JSON Viewer Styles */
        .json-viewer {
            color: #000;
            padding-left: 20px;
        }

        .json-viewer ul {
            list-style-type: none;
            margin: 0;
            margin: 0 0 0 1px;
            border-left: 1px dotted #ccc;
            padding-left: 2em;
        }

        .json-viewer .type-string {
            color: #0B7500;
        }

        .json-viewer .type-date {
            color: #CB7500;
        }

        .json-viewer .type-boolean {
            color: #1A01CC;
            font-weight: bold;
        }

        .json-viewer .type-number {
            color: #1A01CC;
        }

        .json-viewer .type-null {
            color: #90A;
        }

        .json-viewer a.list-link {
            color: #000;
            text-decoration: none;
            position: relative;
        }

        .json-viewer a.list-link:before {
            color: #aaa;
            content: "▶";
            position: absolute;
            left: -1em;
        }

        .json-viewer a.list-link.collapsed:before {
            content: "▼";
        }

        .json-viewer a.list-link.empty:before {
            content: "";
        }

        .json-viewer .items-ph {
            color: #aaa;
            cursor: pointer;
        }

        .json-viewer .items-ph:hover {
            text-decoration: underline;
        }
        .vehicle-card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .vehicle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .vehicle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .vehicle-card:hover::before {
            transform: scaleX(1);
        }

        .vehicle-card.selected {
            border: 3px solid #28a745 !important;
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
            transform: translateY(-5px) scale(1.02) !important;
        }

        .vehicle-card.selected::before {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%) !important;
            transform: scaleX(1) !important;
            height: 6px !important;
        }

        .vehicle-card.selected .vehicle-title {
            font-weight: 700 !important;
            color: #155724 !important;
            font-size: 1.15rem !important;
        }

        .vehicle-card.selected .vehicle-meta {
            font-weight: 600 !important;
            color: #2d5a3d !important;
        }

        .vehicle-card.selected .vehicle-meta strong {
            color: #155724 !important;
        }

        .vehicle-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .vehicle-details {
            flex: 1;
        }

        .vehicle-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.3;
            transition: all 0.3s ease;
        }

        .vehicle-meta {
            font-size: 0.85rem;
            color: #6c757d;
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .vehicle-meta strong {
            color: #495057;
            font-weight: 600;
        }

        .vehicle-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;
        }

        .brand-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .vehicle-card.selected .brand-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .selection-indicator {
            color: #28a745;
            font-size: 1.5rem;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .vehicle-card.selected .selection-indicator {
            opacity: 1;
            transform: scale(1);
        }

        /* Ensure styles are applied with higher specificity */
        .card-body .vehicle-card,
        div[class*="vehicle-card"] {
            border: 1px solid #e3e6f0 !important;
            border-radius: 12px !important;
            padding: 20px !important;
            margin-bottom: 15px !important;
            cursor: pointer !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
            position: relative !important;
            overflow: hidden !important;
            min-height: 120px !important;
        }

        .card-body .vehicle-card:hover,
        div[class*="vehicle-card"]:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            border-color: #667eea !important;
        }

        .card-body .vehicle-card.selected,
        div[class*="vehicle-card"].selected {
            border: 3px solid #28a745 !important;
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
            transform: translateY(-5px) scale(1.02) !important;
        }

        .card-body .vehicle-card.selected .vehicle-title,
        div[class*="vehicle-card"].selected .vehicle-title {
            font-weight: 700 !important;
            color: #155724 !important;
            font-size: 1.15rem !important;
        }

        .card-body .vehicle-card.selected .vehicle-meta,
        div[class*="vehicle-card"].selected .vehicle-meta {
            font-weight: 600 !important;
            color: #2d5a3d !important;
        }

        /* Test style to verify CSS is loading */
        .vehicle-card {
            background-color: #f0f8ff !important; /* Light blue background for testing */
        }

        .json-viewer-container {
            max-height: 700px;
            overflow-y: auto;
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            padding: 0;
            background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .json-viewer-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .json-viewer-controls .btn {
            border-color: rgba(255,255,255,0.3);
            color: white;
            margin-left: 2px;
        }

        .json-viewer-controls .btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            color: white;
        }

        .json-viewer-stats {
            background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e3e6f0;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }

        .stat-value {
            color: #495057;
            font-weight: 600;
        }

        .json-viewer-content {
            padding: 20px;
            background-color: #fafbfc;
            border-radius: 0 0 12px 12px;
        }

        /* Custom scrollbar for JSON viewer */
        .json-viewer-container::-webkit-scrollbar {
            width: 8px;
        }

        .json-viewer-container::-webkit-scrollbar-track {
            background: #f1f3f4;
            border-radius: 4px;
        }

        .json-viewer-container::-webkit-scrollbar-thumb {
            background: #c1c8cd;
            border-radius: 4px;
        }

        .json-viewer-container::-webkit-scrollbar-thumb:hover {
            background: #a8b2ba;
        }

        /* Enhanced Renderjson Styles - Light Theme */
        .json-viewer-content.theme-light .renderjson,
        .json-viewer-content .renderjson {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.8;
            color: #2d3748;
            background: transparent;
        }

        .json-viewer-content.theme-light .renderjson a,
        .json-viewer-content .renderjson a {
            text-decoration: none;
            color: #667eea;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
            transition: all 0.2s ease;
            position: relative;
        }

        .json-viewer-content.theme-light .renderjson a:hover,
        .json-viewer-content .renderjson a:hover {
            background-color: #667eea;
            color: white;
            text-decoration: none;
            transform: scale(1.05);
        }

        .json-viewer-content.theme-light .renderjson .disclosure,
        .json-viewer-content .renderjson .disclosure {
            color: #667eea;
            font-weight: bold;
            margin-right: 8px;
            font-size: 14px;
            width: 16px;
            display: inline-block;
            text-align: center;
            cursor: pointer;
        }

        .json-viewer-content.theme-light .renderjson .syntax,
        .json-viewer-content .renderjson .syntax {
            color: #718096;
            font-weight: 600;
        }

        .json-viewer-content.theme-light .renderjson .string,
        .json-viewer-content .renderjson .string {
            color: #38a169;
            font-weight: 500;
        }

        .json-viewer-content.theme-light .renderjson .number,
        .json-viewer-content .renderjson .number {
            color: #3182ce;
            font-weight: 600;
        }

        .json-viewer-content.theme-light .renderjson .boolean,
        .json-viewer-content .renderjson .boolean {
            color: #805ad5;
            font-weight: 700;
            text-transform: uppercase;
            font-size: 11px;
            background: rgba(128, 90, 213, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
        }

        .json-viewer-content.theme-light .renderjson .key,
        .json-viewer-content .renderjson .key {
            color: #d53f8c;
            font-weight: 600;
        }

        .json-viewer-content.theme-light .renderjson .keyword,
        .json-viewer-content .renderjson .keyword {
            color: #a0aec0;
            font-style: italic;
        }

        /* Dark Theme */
        .json-viewer-content.theme-dark {
            background: #1a202c;
            color: #e2e8f0;
        }

        .json-viewer-content.theme-dark .renderjson {
            color: #e2e8f0;
        }

        .json-viewer-content.theme-dark .renderjson a {
            color: #90cdf4;
        }

        .json-viewer-content.theme-dark .renderjson a:hover {
            background-color: #90cdf4;
            color: #1a202c;
        }

        .json-viewer-content.theme-dark .renderjson .disclosure {
            color: #90cdf4;
        }

        .json-viewer-content.theme-dark .renderjson .syntax {
            color: #a0aec0;
        }

        .json-viewer-content.theme-dark .renderjson .string {
            color: #68d391;
        }

        .json-viewer-content.theme-dark .renderjson .number {
            color: #63b3ed;
        }

        .json-viewer-content.theme-dark .renderjson .boolean {
            color: #b794f6;
            background: rgba(183, 148, 246, 0.2);
        }

        .json-viewer-content.theme-dark .renderjson .key {
            color: #f687b3;
        }

        .json-viewer-content.theme-dark .renderjson .keyword {
            color: #718096;
        }

        /* Enhanced visual elements */
        .renderjson .object,
        .renderjson .array {
            position: relative;
        }

        .renderjson .object:before,
        .renderjson .array:before {
            content: '';
            position: absolute;
            left: -12px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #e2e8f0, transparent);
        }

        .json-viewer-content.theme-dark .renderjson .object:before,
        .json-viewer-content.theme-dark .renderjson .array:before {
            background: linear-gradient(to bottom, #4a5568, transparent);
        }

        /* Type indicators */
        .renderjson .type-indicator {
            font-size: 10px;
            background: #e2e8f0;
            color: #4a5568;
            padding: 1px 4px;
            border-radius: 2px;
            margin-left: 4px;
            font-weight: 500;
        }

        .json-viewer-content.theme-dark .renderjson .type-indicator {
            background: #4a5568;
            color: #e2e8f0;
        }
    </style>
{% endblock %}

{% block content %}
<div class="card">
    {% include '_layout/form_card_header.html.twig' with{
        'title': 'Debug API Form',
    }%}
    
    <div class="card-body">
        {{ form_start(form, {'attr': {'id': 'debug-api-form', 'novalidate': 'novalidate'}}) }}
        
        <div class="row">
            <!-- Optional Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Search Filters (At least one required)</h5>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Please provide at least one filter to search for vehicles. You can combine multiple filters for more specific results.
                </div>
            </div>
            
            <!-- Email Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.email.vars.id }}">{{ form.email.vars.label }}</label>
                    {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Enter email address'}}) }}
                    {{ form_errors(form.email) }}
                </div>
            </div>
            
            <!-- User ID Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.userID.vars.id }}">{{ form.userID.vars.label }}</label>
                    {{ form_widget(form.userID, {'attr': {'class': 'form-control', 'placeholder': 'Enter user ID'}}) }}
                    {{ form_errors(form.userID) }}
                </div>
            </div>
            
            <!-- VIN Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.vin.vars.id }}">{{ form.vin.vars.label }}</label>
                    {{ form_widget(form.vin, {'attr': {'class': 'form-control', 'placeholder': 'Enter VIN'}}) }}
                    {{ form_errors(form.vin) }}
                </div>
            </div>

            <hr class="my-4">

            <!-- Country Filter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.country.vars.id }}">{{ form.country.vars.label }}</label>
                    {{ form_widget(form.country, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.country) }}
                    {% if form.country.vars.help %}
                        <small class="form-text text-muted">{{ form.country.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

             <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.language.vars.id }}">{{ form.language.vars.label }}</label>
                    {{ form_widget(form.language, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.language) }}
                    {% if form.language.vars.help %}
                        <small class="form-text text-muted">{{ form.language.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

            <!-- Source Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.source.vars.id }}">{{ form.source.vars.label }}</label>
                    {{ form_widget(form.source, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.source) }}
                    {% if form.source.vars.help %}
                        <small class="form-text text-muted">{{ form.source.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

            <!-- Target Parameter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.targetParam.vars.id }}">{{ form.targetParam.vars.label }}</label>
                    {{ form_widget(form.targetParam, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.targetParam) }}
                    {% if form.targetParam.vars.help %}
                        <small class="form-text text-muted">{{ form.targetParam.vars.help }}</small>
                    {% endif %}
                </div>
            </div>
        </div>

        <hr class="my-4">
        
        <!-- Submit Button -->
        <div class="card-footer d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i>
                Submit
            </button>
        </div>
        
        {{ form_end(form) }}
    </div>
</div>

{% if error %}
<div class="card mt-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Error</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <strong>Error Code:</strong> {{ error.code }}<br>
            <strong>Message:</strong> {{ error.message }}
        </div>
    </div>
</div>
{% endif %}

{% if vehicles is not empty %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border-radius: 12px;">
    <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px 12px 0 0; border: none;">
        <h5 class="mb-0"><i class="fas fa-car mr-2"></i>Vehicles Found ({{ vehicles|length }})</h5>
        <small style="color: rgba(255,255,255,0.8);">Click on a vehicle to select it (single selection)</small>
    </div>
    <div class="card-body" style="padding: 25px;">
        <div class="row">
            {% for vehicle in vehicles %}
            <div class="col-md-6 col-lg-4">
                <div class="vehicle-card" data-vin="{{ vehicle.vin }}" data-user-id="{{ vehicle.userId }}">
                    <div class="vehicle-info">
                        <div class="vehicle-details">
                            <div class="vehicle-title">{{ vehicle.label }}</div>
                            <div class="vehicle-meta">
                                <div><strong>VIN:</strong> {{ vehicle.vin }}</div>
                                <div><strong>Version ID:</strong> {{ vehicle.versionId }}</div>
                                <div><strong>Owner:</strong> {{ vehicle.userName }} ({{ vehicle.userEmail }})</div>
                                <div><strong>Country:</strong> {{ vehicle.userCountry }}</div>
                                {% if vehicle.mileage != 'N/A' %}
                                <div><strong>Mileage:</strong> {{ vehicle.mileage }} {{ vehicle.mileageUnit }}</div>
                                {% endif %}
                                {% if vehicle.featureCodesCount > 0 %}
                                <div><strong>Features:</strong> {{ vehicle.featureCodesCount }} configured</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="vehicle-actions">
                            <div class="selection-indicator">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <span class="brand-badge">{{ vehicle.brand }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>


    </div>
</div>
{% endif %}

{% if jsonData %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);">
    <div class="card-body p-0">
        <div class="json-viewer-container">
            <div class="json-viewer-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-database mr-2"></i>
                        MongoDB Data Explorer
                    </div>
                    <div class="json-viewer-controls">
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-light" id="expand-all" title="Expand All">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light" id="collapse-all" title="Collapse All">
                                <i class="fas fa-compress-arrows-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light" id="copy-json" title="Copy JSON">
                                <i class="fas fa-copy"></i>
                            </button>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-light dropdown-toggle" data-toggle="dropdown" title="View Options">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <h6 class="dropdown-header">Display Options</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="show-types" checked>
                                            <label class="form-check-label" for="show-types">Show Data Types</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="show-size" checked>
                                            <label class="form-check-label" for="show-size">Show Array/Object Size</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="word-wrap">
                                            <label class="form-check-label" for="word-wrap">Word Wrap</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <h6 class="dropdown-header">Theme</h6>
                                    <a class="dropdown-item theme-option" href="#" data-theme="light">
                                        <i class="fas fa-sun mr-2"></i>Light
                                    </a>
                                    <a class="dropdown-item theme-option" href="#" data-theme="dark">
                                        <i class="fas fa-moon mr-2"></i>Dark
                                    </a>
                                    <a class="dropdown-item theme-option active" href="#" data-theme="auto">
                                        <i class="fas fa-adjust mr-2"></i>Auto
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="json-viewer-content">
                <div class="json-viewer-stats mb-3">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <i class="fas fa-file-alt text-primary"></i>
                                <span class="stat-label">Documents:</span>
                                <span class="stat-value" id="doc-count">-</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <i class="fas fa-car text-success"></i>
                                <span class="stat-label">Vehicles:</span>
                                <span class="stat-value" id="vehicle-count">-</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <i class="fas fa-weight text-info"></i>
                                <span class="stat-label">Size:</span>
                                <span class="stat-value" id="json-size">-</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <i class="fas fa-layer-group text-warning"></i>
                                <span class="stat-label">Depth:</span>
                                <span class="stat-value" id="json-depth">-</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="json-renderer"></div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block script %}
<script src="https://cdn.jsdelivr.net/gh/caldwell/renderjson/renderjson.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Vehicle selection functionality (single selection only)
    let selectedVehicle = null;

    // Debug: Check if vehicle cards exist
    console.log('Vehicle cards found:', $('.vehicle-card').length);

    // Initialize vehicle cards styling
    function initializeVehicleCards() {
        $('.vehicle-card').each(function() {
            $(this).addClass('vehicle-card-initialized');
            // Force apply styles
            $(this).css({
                'border': '1px solid #e3e6f0',
                'border-radius': '12px',
                'padding': '20px',
                'margin-bottom': '15px',
                'cursor': 'pointer',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%)',
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)',
                'position': 'relative',
                'overflow': 'hidden',
                'transition': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            });
        });
        console.log('Vehicle cards initialized with forced styles:', $('.vehicle-card').length);
    }

    // Initialize on page load
    initializeVehicleCards();

    // Also initialize after a short delay to catch any dynamically loaded content
    setTimeout(initializeVehicleCards, 100);

    // Use event delegation to handle dynamically loaded content
    $(document).on('click', '.vehicle-card', function() {
        const vin = $(this).data('vin');
        const userId = $(this).data('user-id');
        const label = $(this).find('.vehicle-title').text();

        if ($(this).hasClass('selected')) {
            // Deselect current vehicle
            $(this).removeClass('selected');
            // Reset styles to default
            $(this).css({
                'border': '1px solid #e3e6f0',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%)',
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)',
                'transform': 'translateY(0) scale(1)'
            });
            selectedVehicle = null;
        } else {
            // Deselect all other vehicles first
            $('.vehicle-card').removeClass('selected');
            $('.vehicle-card').css({
                'border': '1px solid #e3e6f0',
                'background': 'linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%)',
                'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.08)',
                'transform': 'translateY(0) scale(1)'
            });

            // Select this vehicle
            $(this).addClass('selected');
            // Apply selected styles
            $(this).css({
                'border': '3px solid #28a745',
                'background': 'linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%)',
                'box-shadow': '0 12px 40px rgba(40, 167, 69, 0.4)',
                'transform': 'translateY(-5px) scale(1.02)'
            });

            // Style the title and meta for selected card
            $(this).find('.vehicle-title').css({
                'font-weight': '700',
                'color': '#155724',
                'font-size': '1.15rem'
            });

            $(this).find('.vehicle-meta').css({
                'font-weight': '600',
                'color': '#2d5a3d'
            });

            selectedVehicle = {
                vin: vin,
                userId: userId,
                label: label
            };
        }

        // Log selected vehicle to console for debugging (optional)
        console.log('Selected vehicle:', selectedVehicle);
    });

    // Enhanced JSON Viewer
    {% if jsonData %}
    let jsonData = null;
    let currentTheme = 'auto';

    try {
        // Parse JSON from Twig variable
        jsonData = JSON.parse(`{{ jsonData|e('js') }}`);

        // Initialize JSON viewer
        initializeJsonViewer();

        // Calculate and display statistics
        updateJsonStats(jsonData);

        console.log('Enhanced JSON Viewer initialized successfully');
    } catch (error) {
        console.error('Error initializing JSON viewer:', error);
        document.querySelector("#json-renderer").innerHTML = '<div class="alert alert-danger">Error displaying JSON data: ' + error.message + '</div>';
    }

    function initializeJsonViewer() {
        // Configure renderjson
        renderjson.set_icons('▶', '▼');
        renderjson.set_show_to_level(2);
        renderjson.set_max_string_length(150);
        renderjson.set_sort_objects(false);

        // Render JSON
        renderJson();

        // Setup event listeners
        setupJsonViewerControls();
    }

    function renderJson() {
        const container = document.querySelector("#json-renderer");
        container.innerHTML = '';

        // Add custom renderer for enhanced features
        const jsonElement = renderjson(jsonData);
        container.appendChild(jsonElement);

        // Add type indicators if enabled
        if (document.getElementById('show-types').checked) {
            addTypeIndicators(container);
        }

        // Add size indicators if enabled
        if (document.getElementById('show-size').checked) {
            addSizeIndicators(container);
        }
    }

    function addTypeIndicators(container) {
        // Add type indicators to values
        const strings = container.querySelectorAll('.string');
        strings.forEach(el => {
            if (!el.querySelector('.type-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'type-indicator';
                indicator.textContent = 'str';
                el.appendChild(indicator);
            }
        });

        const numbers = container.querySelectorAll('.number');
        numbers.forEach(el => {
            if (!el.querySelector('.type-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'type-indicator';
                indicator.textContent = 'num';
                el.appendChild(indicator);
            }
        });

        const booleans = container.querySelectorAll('.boolean');
        booleans.forEach(el => {
            if (!el.querySelector('.type-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'type-indicator';
                indicator.textContent = 'bool';
                el.appendChild(indicator);
            }
        });
    }

    function addSizeIndicators(container) {
        // Add size indicators to objects and arrays
        const objects = container.querySelectorAll('.object');
        objects.forEach(el => {
            const keys = el.querySelectorAll('.key');
            if (keys.length > 0 && !el.querySelector('.size-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'type-indicator size-indicator';
                indicator.textContent = `${keys.length} keys`;
                indicator.style.marginLeft = '8px';
                el.insertBefore(indicator, el.firstChild);
            }
        });
    }

    function updateJsonStats(data) {
        // Count documents
        const docCount = data.documents ? data.documents.length : 0;
        document.getElementById('doc-count').textContent = docCount;

        // Count vehicles
        let vehicleCount = 0;
        if (data.documents) {
            data.documents.forEach(doc => {
                if (doc.vehicle && Array.isArray(doc.vehicle)) {
                    vehicleCount += doc.vehicle.length;
                }
            });
        }
        document.getElementById('vehicle-count').textContent = vehicleCount;

        // Calculate size
        const jsonString = JSON.stringify(data);
        const sizeKB = (jsonString.length / 1024).toFixed(2);
        document.getElementById('json-size').textContent = sizeKB + ' KB';

        // Calculate depth
        const depth = getObjectDepth(data);
        document.getElementById('json-depth').textContent = depth + ' levels';
    }

    function getObjectDepth(obj) {
        if (typeof obj !== 'object' || obj === null) return 0;
        let maxDepth = 0;
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                const depth = getObjectDepth(obj[key]);
                maxDepth = Math.max(maxDepth, depth);
            }
        }
        return maxDepth + 1;
    }

    function setupJsonViewerControls() {
        // Expand all
        document.getElementById('expand-all').addEventListener('click', function() {
            const disclosures = document.querySelectorAll('#json-renderer .disclosure');
            disclosures.forEach(disclosure => {
                if (disclosure.textContent === '▶') {
                    disclosure.click();
                }
            });
        });

        // Collapse all
        document.getElementById('collapse-all').addEventListener('click', function() {
            const disclosures = document.querySelectorAll('#json-renderer .disclosure');
            disclosures.forEach(disclosure => {
                if (disclosure.textContent === '▼') {
                    disclosure.click();
                }
            });
        });

        // Copy JSON
        document.getElementById('copy-json').addEventListener('click', function() {
            const jsonString = JSON.stringify(jsonData, null, 2);
            navigator.clipboard.writeText(jsonString).then(() => {
                // Show success feedback
                const btn = this;
                const originalIcon = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-outline-light');

                setTimeout(() => {
                    btn.innerHTML = originalIcon;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-light');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy JSON:', err);
            });
        });

        // Theme switcher
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const theme = this.dataset.theme;
                setTheme(theme);

                // Update active state
                document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Display options
        document.getElementById('show-types').addEventListener('change', renderJson);
        document.getElementById('show-size').addEventListener('change', renderJson);

        document.getElementById('word-wrap').addEventListener('change', function() {
            const container = document.querySelector('#json-renderer');
            if (this.checked) {
                container.style.whiteSpace = 'pre-wrap';
                container.style.wordBreak = 'break-word';
            } else {
                container.style.whiteSpace = 'pre';
                container.style.wordBreak = 'normal';
            }
        });
    }

    function setTheme(theme) {
        currentTheme = theme;
        const content = document.querySelector('.json-viewer-content');

        // Remove existing theme classes
        content.classList.remove('theme-light', 'theme-dark');

        if (theme === 'dark') {
            content.classList.add('theme-dark');
        } else if (theme === 'light') {
            content.classList.add('theme-light');
        } else {
            // Auto theme - detect system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                content.classList.add('theme-dark');
            } else {
                content.classList.add('theme-light');
            }
        }
    }

    // Initialize with auto theme
    setTheme('auto');
    {% endif %}

    // Form validation
    $('#debug-api-form').on('submit', function(e) {
        var email = $('#{{ form.email.vars.id }}').val();
        var userID = $('#{{ form.userID.vars.id }}').val();
        var vin = $('#{{ form.vin.vars.id }}').val();
        var country = $('#{{ form.country.vars.id }}').val();

        // Check if at least one filter field is filled
        if (!email && !userID && !vin && !country) {
            e.preventDefault();

            // Show error message
            if ($('.identification-error').length === 0) {
                $('.alert-info').after(
                    '<div class="alert alert-danger identification-error">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Please provide at least one filter (Email, User ID, VIN, or Country).' +
                    '</div>'
                );
            }

            // Scroll to top to show error
            $('html, body').animate({
                scrollTop: $('.identification-error').offset().top - 100
            }, 500);

            return false;
        }

        // Remove any existing error messages
        $('.identification-error').remove();
    });

    // Remove error message when user starts typing in any filter field
    $('#{{ form.email.vars.id }}, #{{ form.userID.vars.id }}, #{{ form.vin.vars.id }}, #{{ form.country.vars.id }}').on('input change', function() {
        $('.identification-error').remove();
    });
});
</script>
{% endblock %}