{% extends '_layout/layout.html.twig' %}

{% block title %}Debug API Form{% endblock %}

{% block content %}
<div class="card">
    {% include '_layout/form_card_header.html.twig' with{
        'title': 'Debug API Form',
    }%}
    
    <div class="card-body">
        {{ form_start(form, {'attr': {'id': 'debug-api-form', 'novalidate': 'novalidate'}}) }}
        
        <div class="row">
            <!-- Optional Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Identification (At least one required)</h5>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Please provide at least one of the following identification fields:
                </div>
            </div>
            
            <!-- Email Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.email.vars.id }}">{{ form.email.vars.label }}</label>
                    {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Enter email address'}}) }}
                    {{ form_errors(form.email) }}
                </div>
            </div>
            
            <!-- User ID Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.userID.vars.id }}">{{ form.userID.vars.label }}</label>
                    {{ form_widget(form.userID, {'attr': {'class': 'form-control', 'placeholder': 'Enter user ID'}}) }}
                    {{ form_errors(form.userID) }}
                </div>
            </div>
            
            <!-- VIN Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.vin.vars.id }}">{{ form.vin.vars.label }}</label>
                    {{ form_widget(form.vin, {'attr': {'class': 'form-control', 'placeholder': 'Enter VIN'}}) }}
                    {{ form_errors(form.vin) }}
                </div>
            </div>
        </div>
        
        <hr class="my-4">
        
        <div class="row">
            <!-- Required Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Required Information</h5>
            </div>
            
            <!-- Country Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.country.vars.id }}">
                        {{ form.country.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.country, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.country) }}
                </div>
            </div>
            
            <!-- Language Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.language.vars.id }}">
                        {{ form.language.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.language, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.language) }}
                </div>
            </div>
            
            <!-- Source Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.source.vars.id }}">
                        {{ form.source.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.source, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.source) }}
                </div>
            </div>
            
            <!-- Target Parameter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.targetParam.vars.id }}">
                        {{ form.targetParam.vars.label }}
                        <span class="text-danger">*</span>
                    </label>
                    {{ form_widget(form.targetParam, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.targetParam) }}
                </div>
            </div>
        </div>
        
        <!-- Submit Button -->
        <div class="card-footer d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i>
                Submit
            </button>
        </div>
        
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}

{% block script %}
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });
    
    // Form validation
    $('#debug-api-form').on('submit', function(e) {
        var email = $('#{{ form.email.vars.id }}').val();
        var userID = $('#{{ form.userID.vars.id }}').val();
        var vin = $('#{{ form.vin.vars.id }}').val();
        
        // Check if at least one identification field is filled
        if (!email && !userID && !vin) {
            e.preventDefault();
            
            // Show error message
            if ($('.identification-error').length === 0) {
                $('.alert-info').after(
                    '<div class="alert alert-danger identification-error">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Please provide at least one identification field (Email, User ID, or VIN).' +
                    '</div>'
                );
            }
            
            // Scroll to top to show error
            $('html, body').animate({
                scrollTop: $('.identification-error').offset().top - 100
            }, 500);
            
            return false;
        }
        
        // Remove any existing error messages
        $('.identification-error').remove();
    });
    
    // Remove error message when user starts typing in any identification field
    $('#{{ form.email.vars.id }}, #{{ form.userID.vars.id }}, #{{ form.vin.vars.id }}').on('input', function() {
        $('.identification-error').remove();
    });
});
</script>
{% endblock %}