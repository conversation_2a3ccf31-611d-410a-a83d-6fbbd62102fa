{# JavaScript Component #}
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    console.log('Debug API Scripts initialized');

    // Modern JSON Viewer
    {% if jsonData %}
    let jsonData = null;
    let currentJsonData = null;

    try {
        // Parse JSON from Twig variable
        jsonData = JSON.parse(`{{ jsonData|e('js') }}`);
        currentJsonData = jsonData;

        // Initialize modern JSON viewer
        initializeModernJsonViewer();

        // Calculate and display statistics
        updateJsonStats(jsonData);

        console.log('Modern JSON Viewer initialized successfully');
    } catch (error) {
        console.error('Error initializing JSON viewer:', error);
        showJsonError('Error displaying JSON data: ' + error.message);
    }

    function initializeModernJsonViewer() {
        if (currentJsonData) {
            renderJsonContent(currentJsonData);
            setupModernJsonControls();
        }
    }

    function renderJsonContent(data) {
        const container = document.getElementById('json-content');
        if (!container) return;

        try {
            const jsonTree = createJsonTree(data);
            container.innerHTML = `<div class="json-tree">${jsonTree}</div>`;

            // Add click handlers for expandable items
            container.querySelectorAll('.json-expandable').forEach(item => {
                item.addEventListener('click', toggleJsonNode);
            });
        } catch (error) {
            console.error('Error rendering JSON:', error);
            showJsonError('Error rendering JSON: ' + error.message);
        }
    }

    function createJsonTree(obj, depth = 0, key = null) {
        const indent = '  '.repeat(depth);
        const nextIndent = '  '.repeat(depth + 1);

        if (obj === null) {
            return `<span class="json-null">null</span>`;
        }

        if (typeof obj === 'string') {
            return `<span class="json-string">"${escapeHtml(obj)}"</span>`;
        }

        if (typeof obj === 'number') {
            return `<span class="json-number">${obj}</span>`;
        }

        if (typeof obj === 'boolean') {
            return `<span class="json-boolean">${obj}</span>`;
        }

        if (Array.isArray(obj)) {
            if (obj.length === 0) {
                return `<span class="json-bracket">[]</span>`;
            }

            const items = obj.map((item, index) => {
                const itemContent = createJsonTree(item, depth + 1);
                return `${nextIndent}${itemContent}${index < obj.length - 1 ? ',' : ''}`;
            }).join('\n');

            return `<span class="json-expandable" data-depth="${depth}">
                <span class="json-expand-icon expanded">▼</span><span class="json-bracket">[</span> <small class="text-muted">${obj.length} items</small>
                <div class="json-content-block">
\n${items}\n${indent}</div><span class="json-bracket">]</span>
            </span>`;
        }

        if (typeof obj === 'object') {
            const keys = Object.keys(obj);
            if (keys.length === 0) {
                return `<span class="json-bracket">{}</span>`;
            }

            const items = keys.map((k, index) => {
                const value = createJsonTree(obj[k], depth + 1, k);
                return `${nextIndent}<span class="json-key">"${escapeHtml(k)}"</span>: ${value}${index < keys.length - 1 ? ',' : ''}`;
            }).join('\n');

            return `<span class="json-expandable" data-depth="${depth}">
                <span class="json-expand-icon expanded">▼</span><span class="json-bracket">{</span> <small class="text-muted">${keys.length} keys</small>
                <div class="json-content-block">
\n${items}\n${indent}</div><span class="json-bracket">}</span>
            </span>`;
        }

        return `<span class="json-unknown">${escapeHtml(String(obj))}</span>`;
    }

    function toggleJsonNode(event) {
        event.stopPropagation();
        const expandable = event.currentTarget;
        const icon = expandable.querySelector('.json-expand-icon');
        const content = expandable.querySelector('.json-content-block');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.textContent = '▼';
            icon.classList.add('expanded');
        } else {
            content.style.display = 'none';
            icon.textContent = '▶';
            icon.classList.remove('expanded');
        }
    }

    function updateJsonStats(data) {
        try {
            // Count documents
            const docCount = data.documents ? data.documents.length :
                           Array.isArray(data) ? data.length :
                           typeof data === 'object' ? 1 : 0;
            const docCountEl = document.getElementById('doc-count');
            if (docCountEl) docCountEl.textContent = docCount;

            // Calculate size
            const jsonString = JSON.stringify(data);
            const sizeKB = (jsonString.length / 1024).toFixed(2);
            const responseSizeEl = document.getElementById('response-size');
            if (responseSizeEl) responseSizeEl.textContent = sizeKB + ' KB';

            // Update timestamp
            const timestampEl = document.getElementById('response-timestamp');
            if (timestampEl) timestampEl.textContent = new Date().toLocaleString();
        } catch (error) {
            console.error('Error updating JSON stats:', error);
        }
    }

    function showJsonError(message) {
        const container = document.getElementById('json-content');
        if (container) {
            container.innerHTML = `
                <div class="json-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error displaying JSON</p>
                    <small>${escapeHtml(message)}</small>
                </div>
            `;
        }
    }

    function countObjectsAtEachLevel(obj, path = 'root', counts = {}) {
        if (obj === null || obj === undefined) {
            return counts;
        }

        if (Array.isArray(obj)) {
            counts[path] = obj.length;
            obj.forEach((item, index) => {
                if (typeof item === 'object' && item !== null) {
                    countObjectsAtEachLevel(item, `${path}[${index}]`, counts);
                }
            });
        } else if (typeof obj === 'object') {
            const keys = Object.keys(obj);
            counts[path] = keys.length;

            keys.forEach(key => {
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    countObjectsAtEachLevel(obj[key], `${path}.${key}`, counts);
                }
            });
        }

        return counts;
    }

    // Make function globally available
    window.countObjectsAtEachLevel = countObjectsAtEachLevel;

    function displayObjectCounts(counts) {
        try {
            const container = document.getElementById('object-counts');
            if (!container) {
                console.warn('Object counts container not found');
                return;
            }

            container.innerHTML = '';

            // Sort paths by depth and name
            const sortedPaths = Object.keys(counts).sort((a, b) => {
                const depthA = (a.match(/\./g) || []).length;
                const depthB = (b.match(/\./g) || []).length;
                if (depthA !== depthB) return depthA - depthB;
                return a.localeCompare(b);
            });

            sortedPaths.forEach(path => {
                const count = counts[path];
                const countItem = document.createElement('div');
                countItem.className = 'count-item';

                // Determine the type and icon
                let icon = 'fas fa-cube';
                let pathDisplay = path;

                if (path.includes('[]')) {
                    icon = 'fas fa-list';
                    pathDisplay = path.replace('[]', ' (array)');
                } else if (path.includes('[') && path.includes(']')) {
                    icon = 'fas fa-layer-group';
                }

                countItem.innerHTML = `
                    <div class="count-path">
                        <i class="${icon} mr-1"></i>
                        ${escapeHtml(pathDisplay)}
                    </div>
                    <div class="count-value">${count}</div>
                `;

                container.appendChild(countItem);
            });

            // Show the object counts section
            const objectCountsSection = document.getElementById('object-counts-section');
            if (objectCountsSection) {
                objectCountsSection.style.display = 'block';
            }
        } catch (error) {
            console.error('Error displaying object counts:', error);
        }
    }

    // Make function globally available
    window.displayObjectCounts = displayObjectCounts;

    function setupModernJsonControls() {
        // Copy JSON Response
        const copyBtn = document.getElementById('copy-json-response');
        if (copyBtn) {
            copyBtn.addEventListener('click', function() {
                if (currentJsonData) {
                    const jsonString = JSON.stringify(currentJsonData, null, 2);
                    copyToClipboardText(jsonString, this);
                } else {
                    showNotification('No JSON data to copy', 'warning');
                }
            });
        }

        // Expand All
        const expandAllBtn = document.getElementById('expand-all-json');
        if (expandAllBtn) {
            expandAllBtn.addEventListener('click', function() {
                const expandables = document.querySelectorAll('.json-expandable .json-content-block');
                const icons = document.querySelectorAll('.json-expandable .json-expand-icon');

                expandables.forEach(content => {
                    content.style.display = 'block';
                });

                icons.forEach(icon => {
                    icon.textContent = '▼';
                    icon.classList.add('expanded');
                });

                showNotification('All nodes expanded', 'info');
            });
        }

        // Collapse All
        const collapseAllBtn = document.getElementById('collapse-all-json');
        if (collapseAllBtn) {
            collapseAllBtn.addEventListener('click', function() {
                const expandables = document.querySelectorAll('.json-expandable .json-content-block');
                const icons = document.querySelectorAll('.json-expandable .json-expand-icon');

                expandables.forEach(content => {
                    content.style.display = 'none';
                });

                icons.forEach(icon => {
                    icon.textContent = '▶';
                    icon.classList.remove('expanded');
                });

                showNotification('All nodes collapsed', 'info');
            });
        }

        // Format JSON
        const formatBtn = document.getElementById('format-json');
        if (formatBtn) {
            formatBtn.addEventListener('click', function() {
                if (currentJsonData) {
                    renderJsonContent(currentJsonData);
                    showNotification('JSON formatted', 'success');
                }
            });
        }

        // Minify JSON
        const minifyBtn = document.getElementById('minify-json');
        if (minifyBtn) {
            minifyBtn.addEventListener('click', function() {
                if (currentJsonData) {
                    const container = document.getElementById('json-content');
                    const minified = JSON.stringify(currentJsonData);
                    container.innerHTML = `
                        <div class="json-tree">
                            <div class="json-minified">${escapeHtml(minified)}</div>
                        </div>
                    `;
                    showNotification('JSON minified', 'success');
                }
            });
        }

        // Clear JSON Viewer
        const clearBtn = document.getElementById('clear-json-viewer');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                clearJsonViewer();
                showNotification('JSON viewer cleared', 'info');
            });
        }
    }

    function clearJsonViewer() {
        const primaryViewer = document.getElementById('primary-json-viewer');
        if (primaryViewer) {
            primaryViewer.style.display = 'none';
        }

        currentJsonData = null;

        // Reset metadata
        const elements = [
            'response-status', 'metadata-status', 'response-size',
            'doc-count', 'response-timestamp', 'response-time'
        ];

        elements.forEach(id => {
            const el = document.getElementById(id);
            if (el) {
                el.textContent = '-';
                el.className = el.className.replace(/status-\w+/g, '');
            }
        });
    }

    // Enhanced copy function with visual feedback
    function copyToClipboardText(text, button) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showCopySuccess(button);
                showNotification('Copied to clipboard!', 'success');
            }).catch(err => {
                fallbackCopyToClipboard(text, button);
            });
        } else {
            fallbackCopyToClipboard(text, button);
        }
    }

    function showCopySuccess(button) {
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i><span class="btn-text">Copied!</span>';
        button.classList.add('copied');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('copied');
        }, 2000);
    }

        // Theme switcher
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const theme = this.dataset.theme;
                setTheme(theme);

                // Update active state
                document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Display options
        const showCountsEl = document.getElementById('show-counts');
        if (showCountsEl) {
            showCountsEl.addEventListener('change', function() {
                const countsSection = document.querySelector('.object-counts-section');
                if (countsSection) {
                    if (this.checked) {
                        countsSection.style.display = 'block';
                    } else {
                        countsSection.style.display = 'none';
                    }
                }
            });
        }

        const showTypesEl = document.getElementById('show-types');
        if (showTypesEl) {
            showTypesEl.addEventListener('change', function() {
                console.log('Type display toggled:', this.checked);
            });
        }

        const wordWrapEl = document.getElementById('word-wrap');
        if (wordWrapEl) {
            wordWrapEl.addEventListener('change', function() {
                if (jsonViewer) {
                    if (this.checked) {
                        jsonViewer.style.whiteSpace = 'pre-wrap';
                        jsonViewer.style.wordBreak = 'break-word';
                    } else {
                        jsonViewer.style.whiteSpace = 'pre';
                        jsonViewer.style.wordBreak = 'normal';
                    }
                }
            });
        }
    }

    function setTheme(theme) {
        currentTheme = theme;
        const content = document.querySelector('.json-viewer-content');

        if (content) {
            // Remove existing theme classes
            content.classList.remove('theme-light', 'theme-dark', 'theme-auto');

            if (theme === 'dark') {
                content.classList.add('theme-dark');
            } else if (theme === 'light') {
                content.classList.add('theme-light');
            } else {
                // Auto theme - uses CSS media queries
                content.classList.add('theme-auto');
            }
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification-toast`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation-triangle' : 'info'}-circle mr-2"></i>
            ${message}
        `;

        // Add to page
        document.body.appendChild(notification);

        // Position it
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        notification.style.borderRadius = '8px';
        notification.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }

    // Initialize with auto theme
    setTheme('auto');
    {% endif %}
});

// Global variables for API management
let selectedApiGroup = null;
let selectedEndpoint = null;
let currentApiConfig = null;

// Copy to clipboard functionality for vehicle details
function copyToClipboard(elementId, button) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error('Element not found:', elementId);
        return;
    }

    const text = (element.textContent || element.innerText).trim();  // Trim whitespace

    // Try modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopyFeedback(button, true);
        }).catch(err => {
            console.error('Clipboard API failed:', err);
            fallbackCopyToClipboard(text, button);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text, button);
    }
}


// Fallback copy method
function fallbackCopyToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        showCopyFeedback(button, successful);
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showCopyFeedback(button, false);
    }

    document.body.removeChild(textArea);
}

// Show copy feedback
function showCopyFeedback(button, success) {
    const originalIcon = button.innerHTML;
    const originalClass = button.className;

    if (success) {
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('copy-success');
    } else {
        button.innerHTML = '<i class="fas fa-times"></i>';
        button.classList.add('copy-error');
    }

    setTimeout(() => {
        button.innerHTML = originalIcon;
        button.className = originalClass;
    }, 2000);
}

// Select API endpoint (updated for Bootstrap cards)
function selectEndpoint(groupKey, endpointKey) {
    console.log(`Selected endpoint: ${groupKey}.${endpointKey}`);

    selectedApiGroup = groupKey;
    selectedEndpoint = endpointKey;

    // Remove selected class from all endpoint cards
    document.querySelectorAll('.api-endpoint-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selected class to clicked card
    const selectedCard = document.querySelector(`[data-group="${groupKey}"][data-endpoint="${endpointKey}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }

    // Load API configuration
    loadApiConfiguration(groupKey, endpointKey);

    // Show API details section
    document.getElementById('api-details-section').style.display = 'block';

    // Scroll to API details section
    setTimeout(() => {
        document.getElementById('api-details-section').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }, 100);
}

// Load API configuration and populate UI
function loadApiConfiguration(groupKey, endpointKey) {
    {% if externalApis %}
    const apiGroups = {{ externalApis|json_encode|raw }};
    const groupConfig = apiGroups.api_groups[groupKey];
    const endpointConfig = groupConfig.endpoints[endpointKey];

    currentApiConfig = {
        group: groupConfig,
        endpoint: endpointConfig,
        groupKey: groupKey,
        endpointKey: endpointKey
    };

    // Populate API details
    populateApiDetails(currentApiConfig);

    // Populate parameters form
    populateParametersForm(currentApiConfig);
    {% endif %}
}

// Populate API details card
function populateApiDetails(config) {
    const detailsContent = document.getElementById('api-details-content');

    const methodClass = config.endpoint.method.toLowerCase();
    const fullUrl = `${config.group.base_url}${config.endpoint.path}`;

    detailsContent.innerHTML = `
        <div class="api-detail-item d-flex align-items-center">
            <strong>Name:</strong>
            <span>${config.endpoint.name}</span>
        </div>
        <div class="api-detail-item d-flex align-items-center">
            <strong>Method:</strong>
            <span class="method-badge method-${methodClass}">${config.endpoint.method}</span>
        </div>
        <div class="api-detail-item d-flex justify-content-between align-items-center">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="d-flex align-items-center">
                    <strong>URL:</strong>
                    <div class="api-url">${fullUrl}</div>
                </div>
                <button class="copy-btn ml-2" onclick="copyToClipboardText('${fullUrl}', this)" title="Copy URL">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>
        <div class="api-detail-item  d-flex align-items-center">
            <strong>Description:</strong>
            <span>${config.endpoint.description}</span>
        </div>
        <div class="api-detail-item  d-flex  align-items-center">
            <strong>Base URL:</strong>
            <div>${config.group.base_url}</div>
        </div>
        <div class="api-detail-item d-flex align-items-center">
            <strong>Path:</strong>
            <div>${config.endpoint.path}</div>
        </div>
    `;
}

// Populate parameters form
function populateParametersForm(config) {
    const paramsContent = document.getElementById('api-params-content');
    const parameters = config.endpoint.parameters || [];

    if (parameters.length === 0) {
        paramsContent.innerHTML = '<p class="text-muted">No parameters required for this endpoint.</p>';
        document.getElementById('execute-api-btn').disabled = false;
        return;
    }

    let formHtml = '';
    parameters.forEach((param, index) => {
        const isRequired = param.required ? 'required' : '';
        const requiredLabel = param.required ? '<span class="text-danger">*</span>' : '';

        formHtml += `
            <div class="form-group">
                <label for="param-${param.name}">
                    ${param.name} ${requiredLabel}
                    <small class="text-muted">(${param.type})</small>
                </label>
                <input type="text"
                       class="form-control api-param-input"
                       id="param-${param.name}"
                       name="${param.name}"
                       placeholder="${param.description}"
                       ${isRequired}
                       data-required="${param.required}">
                <small class="form-text text-muted">${param.description}</small>
            </div>
        `;
    });

    paramsContent.innerHTML = formHtml;

    // Add event listeners to check if all required params are filled
    document.querySelectorAll('.api-param-input').forEach(input => {
        input.addEventListener('input', checkRequiredParams);
    });

    // Initial check
    checkRequiredParams();
}

// Check if all required parameters are filled
function checkRequiredParams() {
    const requiredInputs = document.querySelectorAll('.api-param-input[data-required="true"]');
    let allFilled = true;

    requiredInputs.forEach(input => {
        if (!input.value.trim()) {
            allFilled = false;
        }
    });

    document.getElementById('execute-api-btn').disabled = !allFilled;
}

// Copy text to clipboard (for URLs, etc.)
function copyToClipboardText(text, button) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopyFeedback(button, true);
        }).catch(err => {
            fallbackCopyToClipboard(text, button);
        });
    } else {
        fallbackCopyToClipboard(text, button);
    }
}

    // Enhanced JSON Viewer with @alenaksu/json-viewer
    {% if jsonData %}
    let jsonData = null;
    let jsonViewer = null;
    let currentTheme = 'auto';
    let searchIterator = null;

    try {
        // Parse JSON from Twig variable
        jsonData = JSON.parse(`{{ jsonData|e('js') }}`);

        // Initialize JSON viewer
        initializeJsonViewer();

        // Calculate and display statistics
        updateJsonStats(jsonData);

        console.log('Enhanced JSON Viewer initialized successfully');
    } catch (error) {
        console.error('Error initializing JSON viewer:', error);
        document.querySelector("#json-renderer").innerHTML = '<div class="alert alert-danger">Error displaying JSON data: ' + error.message + '</div>';
    }

    function initializeJsonViewer() {
        // Get the json-viewer element
        jsonViewer = document.querySelector("#json-renderer");

        // Set the data
        jsonViewer.data = jsonData;

        // Setup event listeners
        setupJsonViewerControls();
    }

    function updateJsonStats(data) {
        // Count documents
        const docCount = data.documents ? data.documents.length : 0;
        document.getElementById('doc-count').textContent = docCount;

        // Count vehicles
        let vehicleCount = 0;
        if (data.documents) {
            data.documents.forEach(doc => {
                if (doc.vehicle && Array.isArray(doc.vehicle)) {
                    vehicleCount += doc.vehicle.length;
                }
            });
        }
        document.getElementById('vehicle-count').textContent = vehicleCount;

        // Calculate size
        const jsonString = JSON.stringify(data);
        const sizeKB = (jsonString.length / 1024).toFixed(2);
        document.getElementById('response-size').textContent = sizeKB + ' KB';

        // Count total objects
        const objectCounts = countObjectsAtEachLevel(data);
        const totalObjects = Object.values(objectCounts).reduce((sum, count) => sum + count, 0);
        document.getElementById('total-objects').textContent = totalObjects;

        // Display object counts breakdown
        displayObjectCounts(objectCounts);
    }
    {% endif %}

    // External APIs functionality
    {% if externalApis %}
    let currentApiKey = null;
    let mongoDocuments = null;
    
    // Store MongoDB documents for API parameter extraction
    {% if jsonData %}
    try {
        mongoDocuments = JSON.parse(`{{ jsonData|e('js') }}`).documents || [];
        console.log('MongoDB documents loaded for API calls:', mongoDocuments.length);
    } catch (e) {
        console.error('Failed to parse MongoDB documents:', e);
    }
    {% endif %}
    
    // API tab switching
    $('#api-tabs .nav-link').on('click', function(e) {
        e.preventDefault();
        currentApiKey = $(this).data('api-key');
        console.log('Selected API:', currentApiKey);
        
        // Enable run button if we have documents and an API selected
        if (currentApiKey && mongoDocuments && mongoDocuments.length > 0) {
            $('#run-api-btn').prop('disabled', false);
        } else {
            $('#run-api-btn').prop('disabled', true);
        }
    });
    
    // Initialize first API as selected
    if ($('#api-tabs .nav-link.active').length > 0) {
        currentApiKey = $('#api-tabs .nav-link.active').data('api-key');
        if (currentApiKey && mongoDocuments && mongoDocuments.length > 0) {
            $('#run-api-btn').prop('disabled', false);
        }
    }
    
    // Run API button click
    $('#run-api-btn').on('click', function() {
        if (!currentApiKey || !mongoDocuments || mongoDocuments.length === 0) {
            alert('No API selected or no MongoDB data available');
            return;
        }
        
        console.log('Running API:', currentApiKey);
        runExternalApi(currentApiKey);
    });
    {% endif %}

    // Form validation
    $('#debug-api-form').on('submit', function(e) {
        var email = $('#{{ form.email.vars.id }}').val();
        var userID = $('#{{ form.userID.vars.id }}').val();
        var vin = $('#{{ form.vin.vars.id }}').val();
        var country = $('#{{ form.country.vars.id }}').val();

        // Check if at least one filter field is filled
        if (!email && !userID && !vin && !country) {
            e.preventDefault();

            // Show error message
            if ($('.identification-error').length === 0) {
                $('.alert-info').after(
                    '<div class="alert alert-danger identification-error">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Please provide at least one filter (Email, User ID, VIN, or Country).' +
                    '</div>'
                );
            }

            // Scroll to top to show error
            $('html, body').animate({
                scrollTop: $('.identification-error').offset().top - 100
            }, 500);

            return false;
        }

        // Remove any existing error messages
        $('.identification-error').remove();
    });

    // Remove error message when user starts typing in any filter field
    $('#{{ form.email.vars.id }}, #{{ form.userID.vars.id }}, #{{ form.vin.vars.id }}, #{{ form.country.vars.id }}').on('input change', function() {
        $('.identification-error').remove();
    });
</script>
