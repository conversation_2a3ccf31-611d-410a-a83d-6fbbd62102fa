{# JavaScript Component #}
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    console.log('Debug API Scripts initialized');
});

// Global variables for API management
let selectedApiGroup = null;
let selectedEndpoint = null;
let currentApiConfig = null;

// Copy to clipboard functionality for vehicle details
function copyToClipboard(elementId, button) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error('Element not found:', elementId);
        return;
    }

    const text = element.textContent || element.innerText;

    // Try modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopyFeedback(button, true);
        }).catch(err => {
            console.error('Clipboard API failed:', err);
            fallbackCopyToClipboard(text, button);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text, button);
    }
}

// Fallback copy method
function fallbackCopyToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        showCopyFeedback(button, successful);
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showCopyFeedback(button, false);
    }

    document.body.removeChild(textArea);
}

// Show copy feedback
function showCopyFeedback(button, success) {
    const originalIcon = button.innerHTML;
    const originalClass = button.className;

    if (success) {
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('copy-success');
    } else {
        button.innerHTML = '<i class="fas fa-times"></i>';
        button.classList.add('copy-error');
    }

    setTimeout(() => {
        button.innerHTML = originalIcon;
        button.className = originalClass;
    }, 2000);
}

// Toggle API group visibility
function toggleApiGroup(groupKey) {
    const endpointsContainer = document.getElementById(`endpoints-${groupKey}`);
    const toggleIcon = document.getElementById(`toggle-${groupKey}`);

    if (endpointsContainer.style.display === 'none') {
        endpointsContainer.style.display = 'block';
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        endpointsContainer.style.display = 'none';
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
}

// Select API endpoint
function selectEndpoint(groupKey, endpointKey) {
    console.log(`Selected endpoint: ${groupKey}.${endpointKey}`);

    selectedApiGroup = groupKey;
    selectedEndpoint = endpointKey;

    // Remove active class from all endpoint tabs
    document.querySelectorAll('.endpoint-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Add active class to selected tab
    const selectedTab = document.querySelector(`[data-group="${groupKey}"][data-endpoint="${endpointKey}"]`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Load API configuration
    loadApiConfiguration(groupKey, endpointKey);

    // Show API details section
    document.getElementById('api-details-section').style.display = 'block';
}

// Load API configuration and populate UI
function loadApiConfiguration(groupKey, endpointKey) {
    {% if externalApis %}
    const apiGroups = {{ externalApis|json_encode|raw }};
    const groupConfig = apiGroups.api_groups[groupKey];
    const endpointConfig = groupConfig.endpoints[endpointKey];

    currentApiConfig = {
        group: groupConfig,
        endpoint: endpointConfig,
        groupKey: groupKey,
        endpointKey: endpointKey
    };

    // Populate API details
    populateApiDetails(currentApiConfig);

    // Populate parameters form
    populateParametersForm(currentApiConfig);
    {% endif %}
}

// Populate API details card
function populateApiDetails(config) {
    const detailsContent = document.getElementById('api-details-content');

    const methodClass = config.endpoint.method.toLowerCase();
    const fullUrl = `${config.group.base_url}${config.endpoint.path}`;

    detailsContent.innerHTML = `
        <div class="api-detail-item">
            <strong>Name:</strong>
            <span>${config.endpoint.name}</span>
        </div>
        <div class="api-detail-item">
            <strong>Method:</strong>
            <span class="method-badge method-${methodClass}">${config.endpoint.method}</span>
        </div>
        <div class="api-detail-item">
            <strong>URL:</strong>
            <code class="api-url">${fullUrl}</code>
            <button class="copy-btn ml-2" onclick="copyToClipboardText('${fullUrl}', this)" title="Copy URL">
                <i class="fas fa-copy"></i>
            </button>
        </div>
        <div class="api-detail-item">
            <strong>Description:</strong>
            <span>${config.endpoint.description}</span>
        </div>
        <div class="api-detail-item">
            <strong>Base URL:</strong>
            <code>${config.group.base_url}</code>
        </div>
        <div class="api-detail-item">
            <strong>Path:</strong>
            <code>${config.endpoint.path}</code>
        </div>
    `;
}

// Populate parameters form
function populateParametersForm(config) {
    const paramsContent = document.getElementById('api-params-content');
    const parameters = config.endpoint.parameters || [];

    if (parameters.length === 0) {
        paramsContent.innerHTML = '<p class="text-muted">No parameters required for this endpoint.</p>';
        document.getElementById('execute-api-btn').disabled = false;
        return;
    }

    let formHtml = '';
    parameters.forEach((param, index) => {
        const isRequired = param.required ? 'required' : '';
        const requiredLabel = param.required ? '<span class="text-danger">*</span>' : '';

        formHtml += `
            <div class="form-group">
                <label for="param-${param.name}">
                    ${param.name} ${requiredLabel}
                    <small class="text-muted">(${param.type})</small>
                </label>
                <input type="text"
                       class="form-control api-param-input"
                       id="param-${param.name}"
                       name="${param.name}"
                       placeholder="${param.description}"
                       ${isRequired}
                       data-required="${param.required}">
                <small class="form-text text-muted">${param.description}</small>
            </div>
        `;
    });

    paramsContent.innerHTML = formHtml;

    // Add event listeners to check if all required params are filled
    document.querySelectorAll('.api-param-input').forEach(input => {
        input.addEventListener('input', checkRequiredParams);
    });

    // Initial check
    checkRequiredParams();
}

// Check if all required parameters are filled
function checkRequiredParams() {
    const requiredInputs = document.querySelectorAll('.api-param-input[data-required="true"]');
    let allFilled = true;

    requiredInputs.forEach(input => {
        if (!input.value.trim()) {
            allFilled = false;
        }
    });

    document.getElementById('execute-api-btn').disabled = !allFilled;
}

// Copy text to clipboard (for URLs, etc.)
function copyToClipboardText(text, button) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopyFeedback(button, true);
        }).catch(err => {
            fallbackCopyToClipboard(text, button);
        });
    } else {
        fallbackCopyToClipboard(text, button);
    }
}

    // Enhanced JSON Viewer with @alenaksu/json-viewer
    {% if jsonData %}
    let jsonData = null;
    let jsonViewer = null;
    let currentTheme = 'auto';
    let searchIterator = null;

    try {
        // Parse JSON from Twig variable
        jsonData = JSON.parse(`{{ jsonData|e('js') }}`);

        // Initialize JSON viewer
        initializeJsonViewer();

        // Calculate and display statistics
        updateJsonStats(jsonData);

        console.log('Enhanced JSON Viewer initialized successfully');
    } catch (error) {
        console.error('Error initializing JSON viewer:', error);
        document.querySelector("#json-renderer").innerHTML = '<div class="alert alert-danger">Error displaying JSON data: ' + error.message + '</div>';
    }

    function initializeJsonViewer() {
        // Get the json-viewer element
        jsonViewer = document.querySelector("#json-renderer");

        // Set the data
        jsonViewer.data = jsonData;

        // Setup event listeners
        setupJsonViewerControls();
    }

    function updateJsonStats(data) {
        // Count documents
        const docCount = data.documents ? data.documents.length : 0;
        document.getElementById('doc-count').textContent = docCount;

        // Count vehicles
        let vehicleCount = 0;
        if (data.documents) {
            data.documents.forEach(doc => {
                if (doc.vehicle && Array.isArray(doc.vehicle)) {
                    vehicleCount += doc.vehicle.length;
                }
            });
        }
        document.getElementById('vehicle-count').textContent = vehicleCount;

        // Calculate size
        const jsonString = JSON.stringify(data);
        const sizeKB = (jsonString.length / 1024).toFixed(2);
        document.getElementById('response-size').textContent = sizeKB + ' KB';

        // Count total objects
        const objectCounts = countObjectsAtEachLevel(data);
        const totalObjects = Object.values(objectCounts).reduce((sum, count) => sum + count, 0);
        document.getElementById('total-objects').textContent = totalObjects;

        // Display object counts breakdown
        displayObjectCounts(objectCounts);
    }
    {% endif %}

    // External APIs functionality
    {% if externalApis %}
    let currentApiKey = null;
    let mongoDocuments = null;
    
    // Store MongoDB documents for API parameter extraction
    {% if jsonData %}
    try {
        mongoDocuments = JSON.parse(`{{ jsonData|e('js') }}`).documents || [];
        console.log('MongoDB documents loaded for API calls:', mongoDocuments.length);
    } catch (e) {
        console.error('Failed to parse MongoDB documents:', e);
    }
    {% endif %}
    
    // API tab switching
    $('#api-tabs .nav-link').on('click', function(e) {
        e.preventDefault();
        currentApiKey = $(this).data('api-key');
        console.log('Selected API:', currentApiKey);
        
        // Enable run button if we have documents and an API selected
        if (currentApiKey && mongoDocuments && mongoDocuments.length > 0) {
            $('#run-api-btn').prop('disabled', false);
        } else {
            $('#run-api-btn').prop('disabled', true);
        }
    });
    
    // Initialize first API as selected
    if ($('#api-tabs .nav-link.active').length > 0) {
        currentApiKey = $('#api-tabs .nav-link.active').data('api-key');
        if (currentApiKey && mongoDocuments && mongoDocuments.length > 0) {
            $('#run-api-btn').prop('disabled', false);
        }
    }
    
    // Run API button click
    $('#run-api-btn').on('click', function() {
        if (!currentApiKey || !mongoDocuments || mongoDocuments.length === 0) {
            alert('No API selected or no MongoDB data available');
            return;
        }
        
        console.log('Running API:', currentApiKey);
        runExternalApi(currentApiKey);
    });
    {% endif %}

    // Form validation
    $('#debug-api-form').on('submit', function(e) {
        var email = $('#{{ form.email.vars.id }}').val();
        var userID = $('#{{ form.userID.vars.id }}').val();
        var vin = $('#{{ form.vin.vars.id }}').val();
        var country = $('#{{ form.country.vars.id }}').val();

        // Check if at least one filter field is filled
        if (!email && !userID && !vin && !country) {
            e.preventDefault();

            // Show error message
            if ($('.identification-error').length === 0) {
                $('.alert-info').after(
                    '<div class="alert alert-danger identification-error">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    'Please provide at least one filter (Email, User ID, VIN, or Country).' +
                    '</div>'
                );
            }

            // Scroll to top to show error
            $('html, body').animate({
                scrollTop: $('.identification-error').offset().top - 100
            }, 500);

            return false;
        }

        // Remove any existing error messages
        $('.identification-error').remove();
    });

    // Remove error message when user starts typing in any filter field
    $('#{{ form.email.vars.id }}, #{{ form.userID.vars.id }}, #{{ form.vin.vars.id }}, #{{ form.country.vars.id }}').on('input change', function() {
        $('.identification-error').remove();
    });
</script>
