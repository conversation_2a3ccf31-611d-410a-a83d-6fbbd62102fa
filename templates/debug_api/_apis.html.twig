{# External APIs Component #}
{% if jsonData and externalApis %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border-radius: 12px;">
    <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 12px 12px 0 0; border: none;">
        <h5 class="mb-0"><i class="fas fa-plug mr-2"></i>External APIs ({{ externalApis.external_apis.apis|length }})</h5>
        <small style="color: rgba(255,255,255,0.8);">Select an API and click Run to test with current MongoDB data</small>
    </div>
    <div class="card-body" style="padding: 25px;">
        <div class="row">
            <div class="col-md-8">
                <div class="api-tabs-container">
                    <div class="nav nav-pills mb-3" id="api-tabs" role="tablist">
                        {% for api<PERSON>ey, apiConfig in externalApis.external_apis.apis %}
                        <a class="nav-link {% if loop.first %}active{% endif %}" 
                           id="{{ apiKey }}-tab" 
                           data-toggle="pill" 
                           href="#{{ apiKey }}-content" 
                           role="tab" 
                           data-api-key="{{ apiKey }}">
                            <i class="fas fa-code mr-2"></i>{{ apiConfig.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-right">
                <button type="button" class="btn btn-success btn-lg" id="run-api-btn" disabled>
                    <i class="fas fa-play mr-2"></i>Run API
                </button>
            </div>
        </div>
        
        <div class="tab-content" id="api-tab-content">
            {% for apiKey, apiConfig in externalApis.external_apis.apis %}
            <div class="tab-pane fade {% if loop.first %}show active{% endif %}" 
                 id="{{ apiKey }}-content" 
                 role="tabpanel">
                <div class="api-details-card">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fas fa-info-circle mr-2"></i>API Details</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Endpoint:</strong></td>
                                    <td><code>{{ apiConfig.endpoint }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Method:</strong></td>
                                    <td><span class="badge badge-primary">{{ apiConfig.method }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Description:</strong></td>
                                    <td>{{ apiConfig.description }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success"><i class="fas fa-cogs mr-2"></i>Required Parameters</h6>
                            <div class="params-list">
                                {% for param in apiConfig.required_params %}
                                <div class="param-item">
                                    <span class="param-name">{{ param }}</span>
                                    <span class="param-mapping">← {{ apiConfig.param_mapping[param] }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
