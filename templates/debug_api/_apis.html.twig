{# External APIs Swagger Interface #}
{% if externalApis %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border-radius: 12px;">
    <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 12px 12px 0 0; border: none;">
        <h5 class="mb-0"><i class="fas fa-code mr-2"></i>API Swagger Interface</h5>
        <small style="color: rgba(255,255,255,0.8);">Select API groups and endpoints to test</small>
    </div>
    <div class="card-body" style="padding: 25px;">
        <!-- API Groups Dropdown -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="api-groups-container">
                    {% for groupKey, groupConfig in externalApis.api_groups %}
                    <div class="api-group-card" data-group="{{ groupKey }}">
                        <div class="api-group-header" onclick="toggleApiGroup('{{ groupKey }}')">
                            <div class="group-info">
                                <h6 class="group-title">
                                    <i class="fas fa-server mr-2"></i>{{ groupConfig.name }}
                                </h6>
                                <small class="group-description">{{ groupConfig.description }}</small>
                                <div class="group-meta">
                                    <span class="base-url">{{ groupConfig.base_url }}</span>
                                    <span class="endpoint-count">{{ groupConfig.endpoints|length }} endpoints</span>
                                </div>
                            </div>
                            <div class="group-toggle">
                                <i class="fas fa-chevron-down" id="toggle-{{ groupKey }}"></i>
                            </div>
                        </div>

                        <div class="api-endpoints-container" id="endpoints-{{ groupKey }}" style="display: none;">
                            <div class="endpoints-tabs">
                                {% for endpointKey, endpointConfig in groupConfig.endpoints %}
                                <button class="endpoint-tab"
                                        data-group="{{ groupKey }}"
                                        data-endpoint="{{ endpointKey }}"
                                        onclick="selectEndpoint('{{ groupKey }}', '{{ endpointKey }}')">
                                    <span class="method-badge method-{{ endpointConfig.method|lower }}">{{ endpointConfig.method }}</span>
                                    <span class="endpoint-path">{{ endpointConfig.path }}</span>
                                </button>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Selected API Details and Parameters -->
        <div class="row" id="api-details-section" style="display: none;">
            <div class="col-md-6">
                <!-- API Details Card -->
                <div class="card api-details-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-info-circle mr-2"></i>API Details</h6>
                    </div>
                    <div class="card-body">
                        <div id="api-details-content">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <!-- Parameters Input Card -->
                <div class="card api-params-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-cogs mr-2"></i>Parameters</h6>
                    </div>
                    <div class="card-body">
                        <form id="api-params-form">
                            <div id="api-params-content">
                                <!-- Will be populated by JavaScript -->
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-success btn-block" id="execute-api-btn" disabled>
                                    <i class="fas fa-play mr-2"></i>Execute API
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Response Section -->
        <div class="row mt-4" id="api-response-section" style="display: none;">
            <div class="col-md-12">
                <div class="card api-response-card">
                    <div class="card-header bg-info text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-terminal mr-2"></i>API Response</h6>
                            <div class="response-controls">
                                <button class="btn btn-sm btn-outline-light" onclick="copyApiResponse()" title="Copy Response">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-light" onclick="clearApiResponse()" title="Clear Response">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="api-response-content">
                            <!-- API response will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
