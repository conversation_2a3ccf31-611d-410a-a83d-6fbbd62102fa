{# API Functions with Enhanced Console Logging #}
<script>
// Helper function to get status text
function getStatusText(status) {
    const statusTexts = {
        200: 'OK',
        201: 'Created',
        204: 'No Content',
        400: 'Bad Request',
        401: 'Unauthorized',
        403: 'Forbidden',
        404: 'Not Found',
        405: 'Method Not Allowed',
        429: 'Too Many Requests',
        500: 'Internal Server Error',
        502: 'Bad Gateway',
        503: 'Service Unavailable',
        504: 'Gateway Timeout'
    };
    return statusTexts[status] || 'Unknown Status';
}

// Execute API function
function executeApi() {
    if (!currentApiConfig) {
        alert('No API selected');
        return;
    }

    console.group(`🚀 Executing API: ${currentApiConfig.endpoint.name}`);

    // Collect parameters
    const params = {};
    const paramInputs = document.querySelectorAll('.api-param-input');

    paramInputs.forEach(input => {
        if (input.value.trim()) {
            params[input.name] = input.value.trim();
        }
    });

    console.log('📋 Parameters:', params);

    // Build URL
    const baseUrl = currentApiConfig.group.base_url;
    const path = currentApiConfig.endpoint.path;
    const method = currentApiConfig.endpoint.method;

    // Store original URL for display purposes
    const originalUrl = `${baseUrl}${path}`;
    let displayUrl = originalUrl;

    // For GET requests, add parameters as query string to display URL
    if (method === 'GET' && Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString();
        displayUrl += `?${queryString}`;
    }

    // Use simple proxy endpoint to avoid CORS issues
    const proxyUrl = '/api/proxy/simple';
    const fullUrl = `${proxyUrl}?url=${encodeURIComponent(displayUrl)}`;

    // Alternative: Direct URL (will cause CORS issues)
    // let fullUrl = displayUrl;

    console.log('🔗 Full URL:', displayUrl);
    console.log('📡 Method:', method);

    // Show loading state
    const executeBtn = document.getElementById('execute-api-btn');
    if (!executeBtn) {
        console.error('Execute button not found');
        return;
    }

    const originalText = executeBtn.innerHTML;
    executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Executing...';
    executeBtn.disabled = true;

    // Make actual API call
    const startTime = Date.now();

    fetch(fullUrl, {
        method: 'GET', // Simple GET to proxy
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        const endTime = Date.now();
        const responseTime = `${endTime - startTime}ms`;

        console.log(`📡 Response Status: ${response.status} ${response.statusText}`);
        console.log(`⏱️ Response Time: ${responseTime}`);

        // Parse simple proxy response
        return response.json().then(data => {
            // For simple proxy, the response is the actual API data
            const actualStatus = response.status === 200 ? 200 : response.status;

            return {
                status: actualStatus,
                statusText: getStatusText(actualStatus),
                url: displayUrl,
                method: method,
                parameters: params,
                timestamp: new Date().toISOString(),
                responseTime: responseTime,
                contentType: 'application/json',
                response: data
            };
        }).catch(jsonError => {
            // If JSON parsing fails, return the response as text
            return response.text().then(text => ({
                status: response.status,
                statusText: response.statusText,
                url: displayUrl,
                method: method,
                parameters: params,
                timestamp: new Date().toISOString(),
                responseTime: responseTime,
                contentType: response.headers.get('content-type') || 'text/plain',
                response: {
                    error: {
                        message: 'Invalid JSON response from proxy',
                        details: text,
                        type: 'ParseError'
                    }
                }
            }));
        });
    })
    .then(apiResponse => {
        console.log('📦 API Response Data:', apiResponse.response);
        displayApiResponse(apiResponse);

        console.log('✅ API execution completed successfully');
        console.groupEnd();
    })
    .catch(error => {
        const endTime = Date.now();
        const responseTime = `${endTime - startTime}ms`;

        console.error('❌ API call failed:', error);

        // Create error response
        const errorResponse = {
            status: 0,
            statusText: 'Network Error',
            url: displayUrl,
            method: method,
            parameters: params,
            timestamp: new Date().toISOString(),
            responseTime: responseTime,
            contentType: 'application/json',
            response: {
                error: {
                    message: error.message || 'Network error occurred',
                    type: 'NetworkError',
                    details: error.toString(),
                    // Add CORS hint if it might be a CORS issue
                    hint: error.message && error.message.includes('CORS') ?
                          'This appears to be a CORS issue. Try using a proxy endpoint or enabling CORS on the server.' :
                          undefined
                }
            }
        };

        displayApiResponse(errorResponse);

        console.log('❌ API execution completed with error');
        console.groupEnd();
    })
    .finally(() => {
        // Restore button state
        if (executeBtn) {
            executeBtn.innerHTML = originalText;
            executeBtn.disabled = false;
        }
    });
}

// Display API response in primary JSON viewer
function displayApiResponse(response) {
    // Show the primary JSON viewer
    const primaryViewer = document.getElementById('primary-json-viewer');
    if (primaryViewer) {
        primaryViewer.style.display = 'block';
    }

    // Update status badge
    const statusBadge = document.getElementById('response-status');
    const metadataStatus = document.getElementById('metadata-status');

    // Determine status class based on HTTP status code
    let statusClass = 'secondary';
    if (response.status >= 200 && response.status < 300) {
        statusClass = 'success';
    } else if (response.status >= 400 && response.status < 500) {
        statusClass = 'warning';
    } else if (response.status >= 500 || response.status === 0) {
        statusClass = 'danger';
    }

    if (statusBadge) {
        statusBadge.textContent = response.status === 0 ? 'Network Error' : `${response.status} ${response.statusText}`;
        statusBadge.className = `badge badge-${statusClass} ml-2`;
    }

    if (metadataStatus) {
        const icon = response.status >= 200 && response.status < 300 ? 'check-circle' :
                    response.status === 0 ? 'exclamation-triangle' : 'times-circle';
        const statusText = response.status === 0 ? 'Network Error' : `${response.status} ${response.statusText}`;
        metadataStatus.innerHTML = `<i class="fas fa-${icon} mr-1"></i>${statusText}`;
        metadataStatus.className = `metadata-value status-${statusClass}`;
    }

    // Update metadata
    const contentTypeEl = document.getElementById('metadata-content-type');
    if (contentTypeEl) {
        contentTypeEl.textContent = response.contentType || 'application/json';
    }

    const timestampEl = document.getElementById('response-timestamp');
    if (timestampEl) {
        timestampEl.textContent = new Date(response.timestamp).toLocaleString();
    }

    const responseTimeEl = document.getElementById('response-time');
    if (responseTimeEl) {
        responseTimeEl.textContent = response.responseTime || '~2000ms (simulated)';
    }

    // Update JSON viewer with response data
    const jsonViewer = document.querySelector("#json-renderer");
    if (jsonViewer && response.response) {
        jsonViewer.data = response.response;

        // Update statistics if function exists
        if (typeof updateJsonStats === 'function') {
            updateJsonStats(response.response);
        } else {
            // Fallback: update basic stats
            const responseSizeEl = document.getElementById('response-size');
            if (responseSizeEl) {
                const jsonString = JSON.stringify(response.response);
                const sizeKB = (jsonString.length / 1024).toFixed(2);
                responseSizeEl.textContent = sizeKB + ' KB';
            }
        }
    }

    // Scroll to JSON viewer
    setTimeout(() => {
        primaryViewer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
}

// Copy API response
function copyApiResponse() {
    // Try to get the JSON data from the primary viewer
    const jsonViewer = document.querySelector("#json-renderer");
    let textToCopy = '';

    if (jsonViewer && jsonViewer.data) {
        textToCopy = JSON.stringify(jsonViewer.data, null, 2);
    } else {
        // Fallback to any visible JSON content
        const responseContent = document.getElementById('api-response-content');
        if (responseContent) {
            textToCopy = responseContent.textContent || responseContent.innerText;
        }
    }

    if (textToCopy) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(textToCopy).then(() => {
                if (typeof showNotification === 'function') {
                    showNotification('Response copied to clipboard!', 'success');
                }
            }).catch(err => {
                console.error('Failed to copy response:', err);
                if (typeof showNotification === 'function') {
                    showNotification('Failed to copy response', 'error');
                }
            });
        } else {
            // Fallback copy method
            const textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                if (typeof showNotification === 'function') {
                    showNotification('Response copied to clipboard!', 'success');
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
            }
            document.body.removeChild(textArea);
        }
    } else {
        if (typeof showNotification === 'function') {
            showNotification('No response data to copy', 'warning');
        }
    }
}

// Clear API response
function clearApiResponse() {
    const primaryViewer = document.getElementById('primary-json-viewer');
    if (primaryViewer) {
        primaryViewer.style.display = 'none';
    }

    const jsonViewer = document.querySelector("#json-renderer");
    if (jsonViewer) {
        jsonViewer.data = null;
    }

    if (typeof showNotification === 'function') {
        showNotification('Response cleared', 'info');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle mr-2"></i>
        ${message}
    `;

    // Add to page
    document.body.appendChild(notification);

    // Position it
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Add event listener for execute button
$(document).ready(function() {
    $(document).on('click', '#execute-api-btn', executeApi);
});

</script>
