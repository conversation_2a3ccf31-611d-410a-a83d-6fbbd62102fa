{# API Functions with Enhanced Console Logging #}
<script>
// Function to extract parameter value from MongoDB document with detailed logging
function extractParamValue(document, path, fallbackPath = null) {
    console.group(`🔍 Extracting parameter from path: "${path}"`);
    console.log('📄 Document structure:', document);
    console.log('🗺️ Mapping path:', path);
    if (fallbackPath) {
        console.log('🔄 Fallback path:', fallbackPath);
    }
    
    try {
        let value = null;
        
        // Handle array notation like vehicle[0].vin
        if (path.includes('[') && path.includes(']')) {
            console.log('📋 Processing array notation path');
            const parts = path.split(/[\[\]\.]+/).filter(p => p);
            console.log('🔧 Path parts:', parts);
            
            value = document;
            
            for (let i = 0; i < parts.length; i++) {
                const part = parts[i];
                console.log(`  Step ${i + 1}: Accessing "${part}"`);
                console.log(`  Current value:`, value);
                
                if (isNaN(part)) {
                    value = value[part];
                    console.log(`  → Accessed property "${part}":`, value);
                } else {
                    const index = parseInt(part);
                    value = value[index];
                    console.log(`  → Accessed array index [${index}]:`, value);
                }
                
                if (value === undefined || value === null) {
                    console.warn(`  ⚠️ Value is ${value} at step ${i + 1}`);
                    break;
                }
            }
            
            if ((value === undefined || value === null) && fallbackPath) {
                console.log('🔄 Using fallback path due to null/undefined value');
                console.groupEnd();
                return extractParamValue(document, fallbackPath);
            }
            
        } else {
            console.log('🔗 Processing dot notation path');
            // Handle dot notation like profile.email
            const parts = path.split('.');
            console.log('🔧 Path parts:', parts);
            
            value = document;
            
            for (let i = 0; i < parts.length; i++) {
                const part = parts[i];
                console.log(`  Step ${i + 1}: Processing "${part}"`);
                console.log(`  Current value:`, value);
                
                // Handle dynamic brand replacement like preferredDealer.{brand}.language
                if (part.includes('{') && part.includes('}')) {
                    const brandValue = document.vehicle?.[0]?.brand || 'ap';
                    const dynamicPart = part.replace('{brand}', brandValue);
                    console.log(`  🔄 Dynamic replacement: "${part}" → "${dynamicPart}" (brand: ${brandValue})`);
                    value = value[dynamicPart];
                    console.log(`  → Accessed dynamic property "${dynamicPart}":`, value);
                } else {
                    value = value[part];
                    console.log(`  → Accessed property "${part}":`, value);
                }
                
                if (value === undefined || value === null) {
                    console.warn(`  ⚠️ Value is ${value} at step ${i + 1}`);
                    break;
                }
            }
            
            if ((value === undefined || value === null) && fallbackPath) {
                console.log('🔄 Using fallback path due to null/undefined value');
                console.groupEnd();
                return extractParamValue(document, fallbackPath);
            }
        }
        
        console.log('✅ Final extracted value:', value);
        console.log('📊 Value type:', typeof value);
        console.groupEnd();
        return value;
        
    } catch (e) {
        console.error('❌ Error extracting parameter:', e);
        console.groupEnd();
        
        if (fallbackPath) {
            console.log('🔄 Attempting fallback path due to error');
            return extractParamValue(document, fallbackPath);
        }
        return null;
    }
}

// Function to run external API with detailed parameter logging
function runExternalApi(apiKey) {
    console.group(`🚀 Running External API: "${apiKey}"`);
    
    const apiConfig = {{ externalApis.external_apis|json_encode|raw }};
    const selectedApi = apiConfig.apis[apiKey];
    const baseUrl = apiConfig.base_url;
    
    console.log('🔧 API Configuration:', selectedApi);
    console.log('🌐 Base URL:', baseUrl);
    
    if (!selectedApi || mongoDocuments.length === 0) {
        console.error('❌ API configuration not found or no data available');
        alert('API configuration not found or no data available');
        console.groupEnd();
        return;
    }
    
    // Use first document for parameter extraction
    const document = mongoDocuments[0];
    console.log('📄 Using MongoDB document:', document);
    
    const params = {};
    const parameterLog = [];
    
    console.group('📋 Parameter Extraction Process');
    
    // Extract parameters based on mapping
    for (let param of selectedApi.required_params) {
        console.group(`🔍 Processing parameter: "${param}"`);
        
        const mapping = selectedApi.param_mapping[param];
        const fallbackMapping = selectedApi.fallback_mapping?.[param];
        
        console.log(`📍 Parameter: ${param}`);
        console.log(`🗺️ Mapping: ${mapping}`);
        if (fallbackMapping) {
            console.log(`🔄 Fallback mapping: ${fallbackMapping}`);
        }
        
        const value = extractParamValue(document, mapping, fallbackMapping);
        
        if (value !== null && value !== undefined) {
            params[param] = value;
            parameterLog.push({
                parameter: param,
                mapping: mapping,
                value: value,
                status: '✅ Success'
            });
            console.log(`✅ Successfully extracted: ${param} = "${value}"`);
        } else {
            parameterLog.push({
                parameter: param,
                mapping: mapping,
                value: null,
                status: '❌ Failed'
            });
            console.warn(`❌ Could not extract parameter ${param} from path ${mapping}`);
        }
        
        console.groupEnd();
    }
    
    console.groupEnd(); // End Parameter Extraction Process
    
    // Display parameter extraction summary
    console.group('📊 Parameter Extraction Summary');
    console.table(parameterLog);
    console.log('🎯 Final parameters object:', params);
    console.groupEnd();
    
    // Build URL with parameters
    const url = new URL(selectedApi.endpoint, baseUrl);
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            url.searchParams.append(key, params[key]);
        }
    });
    
    console.log('🔗 Final API URL:', url.toString());
    
    // Create detailed parameter mapping display
    const mappingDisplay = selectedApi.required_params.map(param => {
        const mapping = selectedApi.param_mapping[param];
        const value = params[param];
        const status = value !== null && value !== undefined ? '✅' : '❌';
        return `${status} ${param} ← ${mapping} = "${value || 'N/A'}"`;
    }).join('\n');
    
    console.group('🎯 Parameter Mapping Results');
    console.log(mappingDisplay);
    console.groupEnd();
    
    // Show loading state
    const btn = $('#run-api-btn');
    const originalText = btn.html();
    btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Running...');
    btn.prop('disabled', true);
    
    // For now, just show the URL that would be called
    // In a real implementation, you would make the actual API call
    setTimeout(() => {
        const alertMessage = `API Call Details:\n\n` +
            `🔗 URL: ${url.toString()}\n\n` +
            `📋 Parameter Mapping:\n${mappingDisplay}\n\n` +
            `📊 Parameters Object:\n${JSON.stringify(params, null, 2)}`;
        
        alert(alertMessage);
        
        // Restore button
        btn.html(originalText);
        btn.prop('disabled', false);
        
        console.log('🎉 API execution completed');
        console.groupEnd(); // End Running External API
    }, 1000);
}

// Enhanced console logging for parameter values
function logParameterMapping(apiKey) {
    if (!mongoDocuments || mongoDocuments.length === 0) {
        console.warn('No MongoDB documents available for parameter mapping');
        return;
    }
    
    const apiConfig = {{ externalApis.external_apis|json_encode|raw }};
    const selectedApi = apiConfig.apis[apiKey];
    
    if (!selectedApi) {
        console.error('API configuration not found:', apiKey);
        return;
    }
    
    const document = mongoDocuments[0];
    
    console.group(`🎯 Parameter Mapping for API: "${selectedApi.name}"`);
    console.log('📄 Source document:', document);
    console.log('🔧 API configuration:', selectedApi);
    
    selectedApi.required_params.forEach(param => {
        const mapping = selectedApi.param_mapping[param];
        const fallbackMapping = selectedApi.fallback_mapping?.[param];
        const value = extractParamValue(document, mapping, fallbackMapping);
        
        console.log(`\n📍 ${param} ← ${mapping}`);
        console.log(`   Value: "${value}" (${typeof value})`);
        if (fallbackMapping) {
            console.log(`   Fallback: ${fallbackMapping}`);
        }
    });
    
    console.groupEnd();
}

// Auto-log parameter mapping when API is selected
$(document).ready(function() {
    $('#api-tabs .nav-link').on('shown.bs.tab', function(e) {
        const apiKey = $(e.target).data('api-key');
        if (apiKey) {
            setTimeout(() => logParameterMapping(apiKey), 100);
        }
    });
    
    // Log initial API if one is active
    const initialApiKey = $('#api-tabs .nav-link.active').data('api-key');
    if (initialApiKey) {
        setTimeout(() => logParameterMapping(initialApiKey), 500);
    }
});
</script>
