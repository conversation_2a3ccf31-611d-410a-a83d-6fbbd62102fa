{# API Functions with Enhanced Console Logging #}
<script>
// Execute API function
function executeApi() {
    if (!currentApiConfig) {
        alert('No API selected');
        return;
    }

    console.group(`🚀 Executing API: ${currentApiConfig.endpoint.name}`);

    // Collect parameters
    const params = {};
    const paramInputs = document.querySelectorAll('.api-param-input');

    paramInputs.forEach(input => {
        if (input.value.trim()) {
            params[input.name] = input.value.trim();
        }
    });

    console.log('📋 Parameters:', params);

    // Build URL
    const baseUrl = currentApiConfig.group.base_url;
    const path = currentApiConfig.endpoint.path;
    const method = currentApiConfig.endpoint.method;

    let fullUrl = `${baseUrl}${path}`;

    // For GET requests, add parameters as query string
    if (method === 'GET' && Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString();
        fullUrl += `?${queryString}`;
    }

    console.log('🔗 Full URL:', fullUrl);
    console.log('📡 Method:', method);

    // Show loading state
    const executeBtn = document.getElementById('execute-api-btn');
    const originalText = executeBtn.innerHTML;
    executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Executing...';
    executeBtn.disabled = true;

    // Show response section
    document.getElementById('api-response-section').style.display = 'block';

    // Simulate API call (replace with actual fetch in production)
    setTimeout(() => {
        // Mock response for demonstration
        const mockResponse = {
            status: 200,
            statusText: 'OK',
            url: fullUrl,
            method: method,
            parameters: params,
            timestamp: new Date().toISOString(),
            response: {
                success: true,
                message: 'API call simulated successfully',
                data: {
                    endpoint: currentApiConfig.endpoint.name,
                    group: currentApiConfig.group.name,
                    parameters: params
                }
            }
        };

        displayApiResponse(mockResponse);

        // Restore button
        executeBtn.innerHTML = originalText;
        executeBtn.disabled = false;

        console.log('✅ API execution completed');
        console.groupEnd();
    }, 2000);
}

// Display API response in primary JSON viewer
function displayApiResponse(response) {
    // Show the primary JSON viewer
    const primaryViewer = document.getElementById('primary-json-viewer');
    if (primaryViewer) {
        primaryViewer.style.display = 'block';
    }

    // Update status badge
    const statusBadge = document.getElementById('response-status');
    const metadataStatus = document.getElementById('metadata-status');
    const statusClass = response.status >= 200 && response.status < 300 ? 'success' : 'danger';

    if (statusBadge) {
        statusBadge.textContent = `${response.status} ${response.statusText}`;
        statusBadge.className = `badge badge-${statusClass} ml-2`;
    }

    if (metadataStatus) {
        const icon = response.status >= 200 && response.status < 300 ? 'check-circle' : 'times-circle';
        metadataStatus.innerHTML = `<i class="fas fa-${icon} mr-1"></i>${response.status} ${response.statusText}`;
        metadataStatus.className = `metadata-value status-${statusClass}`;
    }

    // Update metadata
    const contentTypeEl = document.getElementById('metadata-content-type');
    if (contentTypeEl) {
        contentTypeEl.textContent = response.contentType || 'application/json';
    }

    const timestampEl = document.getElementById('response-timestamp');
    if (timestampEl) {
        timestampEl.textContent = new Date(response.timestamp).toLocaleString();
    }

    const responseTimeEl = document.getElementById('response-time');
    if (responseTimeEl) {
        responseTimeEl.textContent = response.responseTime || '~2000ms (simulated)';
    }

    // Update JSON viewer with response data
    const jsonViewer = document.querySelector("#json-renderer");
    if (jsonViewer && response.response) {
        jsonViewer.data = response.response;

        // Update statistics
        updateJsonStats(response.response);
    }

    // Scroll to JSON viewer
    setTimeout(() => {
        primaryViewer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
}

// Copy API response
function copyApiResponse() {
    const responseContent = document.getElementById('api-response-content');
    if (!responseContent) return;

    // Try to get the JSON data from the viewer
    const jsonViewer = document.querySelector("#api-json-viewer");
    let textToCopy = '';

    if (jsonViewer && jsonViewer.data) {
        textToCopy = JSON.stringify(jsonViewer.data, null, 2);
    } else {
        textToCopy = responseContent.textContent || responseContent.innerText;
    }

    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(textToCopy).then(() => {
            showNotification('Response copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy response:', err);
            showNotification('Failed to copy response', 'error');
        });
    } else {
        fallbackCopyToClipboard(textToCopy, null);
    }
}

// Clear API response
function clearApiResponse() {
    const responseContent = document.getElementById('api-response-content');
    if (responseContent) {
        responseContent.innerHTML = '<p class="text-muted">No response data</p>';
    }
    document.getElementById('api-response-section').style.display = 'none';
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle mr-2"></i>
        ${message}
    `;

    // Add to page
    document.body.appendChild(notification);

    // Position it
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Add event listener for execute button
$(document).ready(function() {
    $(document).on('click', '#execute-api-btn', executeApi);
});

</script>
