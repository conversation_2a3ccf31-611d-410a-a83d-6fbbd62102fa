{# API Functions with Enhanced Console Logging #}
<script>
// Execute API function
function executeApi() {
    if (!currentApiConfig) {
        alert('No API selected');
        return;
    }

    console.group(`🚀 Executing API: ${currentApiConfig.endpoint.name}`);

    // Collect parameters
    const params = {};
    const paramInputs = document.querySelectorAll('.api-param-input');

    paramInputs.forEach(input => {
        if (input.value.trim()) {
            params[input.name] = input.value.trim();
        }
    });

    console.log('📋 Parameters:', params);

    // Build URL
    const baseUrl = currentApiConfig.group.base_url;
    const path = currentApiConfig.endpoint.path;
    const method = currentApiConfig.endpoint.method;

    let fullUrl = `${baseUrl}${path}`;

    // For GET requests, add parameters as query string
    if (method === 'GET' && Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString();
        fullUrl += `?${queryString}`;
    }

    console.log('🔗 Full URL:', fullUrl);
    console.log('📡 Method:', method);

    // Show loading state
    const executeBtn = document.getElementById('execute-api-btn');
    const originalText = executeBtn.innerHTML;
    executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Executing...';
    executeBtn.disabled = true;

    // Show response section
    document.getElementById('api-response-section').style.display = 'block';

    // Simulate API call (replace with actual fetch in production)
    setTimeout(() => {
        // Mock response for demonstration
        const mockResponse = {
            status: 200,
            statusText: 'OK',
            url: fullUrl,
            method: method,
            parameters: params,
            timestamp: new Date().toISOString(),
            response: {
                success: true,
                message: 'API call simulated successfully',
                data: {
                    endpoint: currentApiConfig.endpoint.name,
                    group: currentApiConfig.group.name,
                    parameters: params
                }
            }
        };

        displayApiResponse(mockResponse);

        // Restore button
        executeBtn.innerHTML = originalText;
        executeBtn.disabled = false;

        console.log('✅ API execution completed');
        console.groupEnd();
    }, 2000);
}

// Display API response
function displayApiResponse(response) {
    const responseContent = document.getElementById('api-response-content');

    // Create response header
    const statusClass = response.status >= 200 && response.status < 300 ? 'success' : 'danger';

    responseContent.innerHTML = `
        <div class="response-header mb-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="response-meta">
                        <strong>Status:</strong>
                        <span class="badge badge-${statusClass}">${response.status} ${response.statusText}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="response-meta">
                        <strong>Method:</strong>
                        <span class="method-badge method-${response.method.toLowerCase()}">${response.method}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="response-meta">
                        <strong>URL:</strong>
                        <code class="response-url">${response.url}</code>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-6">
                    <div class="response-meta">
                        <strong>Timestamp:</strong>
                        <span>${response.timestamp}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="response-meta">
                        <strong>Response Time:</strong>
                        <span>~2000ms (simulated)</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="response-body">
            <h6><i class="fas fa-code mr-2"></i>Response Body</h6>
            <div class="json-response-container">
                <json-viewer id="api-json-viewer"></json-viewer>
            </div>
        </div>
    `;

    // Initialize JSON viewer for the response
    setTimeout(() => {
        const jsonViewer = document.querySelector("#api-json-viewer");
        if (jsonViewer) {
            jsonViewer.data = response.response;
        }
    }, 100);
}

// Copy API response
function copyApiResponse() {
    const responseContent = document.getElementById('api-response-content');
    if (!responseContent) return;

    // Try to get the JSON data from the viewer
    const jsonViewer = document.querySelector("#api-json-viewer");
    let textToCopy = '';

    if (jsonViewer && jsonViewer.data) {
        textToCopy = JSON.stringify(jsonViewer.data, null, 2);
    } else {
        textToCopy = responseContent.textContent || responseContent.innerText;
    }

    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(textToCopy).then(() => {
            showNotification('Response copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy response:', err);
            showNotification('Failed to copy response', 'error');
        });
    } else {
        fallbackCopyToClipboard(textToCopy, null);
    }
}

// Clear API response
function clearApiResponse() {
    const responseContent = document.getElementById('api-response-content');
    if (responseContent) {
        responseContent.innerHTML = '<p class="text-muted">No response data</p>';
    }
    document.getElementById('api-response-section').style.display = 'none';
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle mr-2"></i>
        ${message}
    `;

    // Add to page
    document.body.appendChild(notification);

    // Position it
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Add event listener for execute button
$(document).ready(function() {
    $(document).on('click', '#execute-api-btn', executeApi);
});

</script>
