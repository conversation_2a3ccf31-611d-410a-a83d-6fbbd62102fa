{# Primary JSON Viewer Component #}
<div class="card mt-4 response-card" id="primary-json-viewer" {% if not jsonData %}style="display: none;"{% endif %}>
    <div class="card-header response-header bg-info text-white">
        <div class="d-flex justify-content-between align-items-center">
            <div class="response-title">
                <i class="fas fa-code mr-2"></i>
                <span class="response-label">JSON Response</span>
                <span class="response-status badge badge-light ml-2" id="response-status">200 OK</span>
            </div>
            <div class="response-controls">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-light" id="expand-all" title="Expand All">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light" id="collapse-all" title="Collapse All">
                        <i class="fas fa-compress-arrows-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light" id="copy-json" title="Copy JSON">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light" id="clear-json" title="Clear Response">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-light dropdown-toggle" data-toggle="dropdown" title="View Options">
                            <i class="fas fa-cog"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-right">
                            <h6 class="dropdown-header">Display Options</h6>
                            <div class="dropdown-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show-counts" checked>
                                    <label class="form-check-label" for="show-counts">Show Object Counts</label>
                                </div>
                            </div>
                            <div class="dropdown-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show-types" checked>
                                    <label class="form-check-label" for="show-types">Show Data Types</label>
                                </div>
                            </div>
                            <div class="dropdown-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="word-wrap">
                                    <label class="form-check-label" for="word-wrap">Word Wrap</label>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <h6 class="dropdown-header">Theme</h6>
                            <a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="fas fa-sun mr-2"></i>Light
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="fas fa-moon mr-2"></i>Dark
                            </a>
                            <a class="dropdown-item theme-option active" href="#" data-theme="auto">
                                <i class="fas fa-adjust mr-2"></i>Auto
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card-body response-body p-0">
        <!-- Response Metadata -->
        <div class="response-metadata p-3 border-bottom bg-light">
            <div class="row">
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-info-circle mr-1"></i>Status
                        </div>
                        <div class="metadata-value status-success" id="metadata-status">
                            <i class="fas fa-check-circle mr-1"></i>200 OK
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-file-code mr-1"></i>Content-Type
                        </div>
                        <div class="metadata-value" id="metadata-content-type">application/json</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-weight mr-1"></i>Size
                        </div>
                        <div class="metadata-value" id="response-size">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-database mr-1"></i>Documents
                        </div>
                        <div class="metadata-value" id="doc-count">-</div>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-car mr-1"></i>Vehicles
                        </div>
                        <div class="metadata-value" id="vehicle-count">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-cubes mr-1"></i>Objects
                        </div>
                        <div class="metadata-value" id="total-objects">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-clock mr-1"></i>Timestamp
                        </div>
                        <div class="metadata-value" id="response-timestamp">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item">
                        <div class="metadata-label">
                            <i class="fas fa-stopwatch mr-1"></i>Response Time
                        </div>
                        <div class="metadata-value" id="response-time">-</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- JSON Viewer -->
        <div class="json-output-section p-3">
            <div class="json-output-container json-viewer-content">
                <json-viewer id="json-renderer"></json-viewer>
            </div>
        </div>
    </div>
</div>
