{# Base Form Component #}
<div class="card">
    {% include '_layout/form_card_header.html.twig' with{
        'title': 'Debug API Form',
    }%}
    
    <div class="card-body">
        {{ form_start(form, {'attr': {'id': 'debug-api-form', 'novalidate': 'novalidate'}}) }}
        
        <div class="row">
            <!-- Optional Fields Section -->
            <div class="col-md-12">
                <h5 class="mb-3">Search Filters (At least one required)</h5>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Please provide at least one filter to search for vehicles. You can combine multiple filters for more specific results.
                </div>
            </div>
            
            <!-- Email Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.email.vars.id }}">{{ form.email.vars.label }}</label>
                    {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Enter email address'}}) }}
                    {{ form_errors(form.email) }}
                </div>
            </div>
            
            <!-- User ID Field -->
            <div class="col-md-4">
                <div class="form-group">
                    <label for="{{ form.userID.vars.id }}">{{ form.userID.vars.label }}</label>
                    {{ form_widget(form.userID, {'attr': {'class': 'form-control', 'placeholder': 'Enter user ID'}}) }}
                    {{ form_errors(form.userID) }}
                </div>
            </div>
            
            <!-- VIN Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.vin.vars.id }}">{{ form.vin.vars.label }}</label>
                    {{ form_widget(form.vin, {'attr': {'class': 'form-control', 'placeholder': 'Enter VIN'}}) }}
                    {{ form_errors(form.vin) }}
                </div>
            </div>

            <hr class="my-4">

            <!-- Country Filter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.country.vars.id }}">{{ form.country.vars.label }}</label>
                    {{ form_widget(form.country, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.country) }}
                    {% if form.country.vars.help %}
                        <small class="form-text text-muted">{{ form.country.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

             <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.language.vars.id }}">{{ form.language.vars.label }}</label>
                    {{ form_widget(form.language, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.language) }}
                    {% if form.language.vars.help %}
                        <small class="form-text text-muted">{{ form.language.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

            <!-- Source Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.source.vars.id }}">{{ form.source.vars.label }}</label>
                    {{ form_widget(form.source, {'attr': {'class': 'form-control select2'}}) }}
                    {{ form_errors(form.source) }}
                    {% if form.source.vars.help %}
                        <small class="form-text text-muted">{{ form.source.vars.help }}</small>
                    {% endif %}
                </div>
            </div>

            <!-- Target Parameter Field -->
            <div class="col-md-3">
                <div class="form-group">
                    <label for="{{ form.targetParam.vars.id }}">{{ form.targetParam.vars.label }}</label>
                    {{ form_widget(form.targetParam, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.targetParam) }}
                    {% if form.targetParam.vars.help %}
                        <small class="form-text text-muted">{{ form.targetParam.vars.help }}</small>
                    {% endif %}
                </div>
            </div>
        </div>

        <hr class="my-4">
        
        <!-- Submit Button -->
        <div class="card-footer d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i>
                Submit
            </button>
        </div>
        
        {{ form_end(form) }}
    </div>
</div>

{% if error %}
<div class="card mt-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Error</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <strong>Error Code:</strong> {{ error.code }}<br>
            <strong>Message:</strong> {{ error.message }}
        </div>
    </div>
</div>
{% endif %}
