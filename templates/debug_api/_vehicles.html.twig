{# Enhanced Vehicles Display Component #}
{% if mongoData and mongoData.documents is not empty %}
<div class="card mt-4 vehicles-container">
    <div class="card-header vehicles-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-1"><i class="fas fa-database mr-2"></i>MongoDB Documents ({{ mongoData.documents|length }})</h5>
                <small class="header-subtitle">Vehicle information with copy functionality</small>
            </div>
            <div class="header-stats">
                {% set totalVehicles = 0 %}
                {% for document in mongoData.documents %}
                    {% if document.vehicle is defined %}
                        {% set totalVehicles = totalVehicles + document.vehicle|length %}
                    {% endif %}
                {% endfor %}
                <span class="badge badge-light badge-lg">
                    <i class="fas fa-car mr-1"></i>{{ totalVehicles }} Vehicles
                </span>
            </div>
        </div>
    </div>
    <div class="card-body vehicles-body">
        {% for document in mongoData.documents %}
            {% if document.vehicle is defined and document.vehicle is not empty %}
                <!-- Document Header -->
                <div class="document-section mb-4">
                    <div class="document-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="document-title">
                                <i class="fas fa-user-circle mr-2"></i>
                                User: {{ document.profile.firstName|default('Unknown') }} {{ document.profile.lastName|default('User') }}
                            </h6>
                            <div class="document-meta">
                                <span class="badge badge-primary">{{ document.vehicle|length }} vehicle(s)</span>
                                <span class="badge badge-secondary">{{ document.profile.country|default('Unknown') }}</span>
                            </div>
                        </div>
                        <div class="document-details mt-2">
                            <small class="text-muted">
                                <i class="fas fa-envelope mr-1"></i>{{ document.profile.email|default('No email') }}
                                <span class="mx-2">•</span>
                                <i class="fas fa-id-card mr-1"></i>{{ document.userId|slice(0, 8) }}...
                            </small>
                        </div>
                    </div>

                    <!-- Vehicles Grid -->
                    <div class="row mt-3">
                        {% for vehicle in document.vehicle %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="vehicle-card">
                                <!-- Vehicle Header -->
                                <div class="vehicle-card-header">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="vehicle-info">
                                            <h6 class="vehicle-name">{{ vehicle.shortLabel|default(vehicle.modelDescription|default('Vehicle')) }}</h6>
                                            <small class="vehicle-type">{{ vehicle.type|default('Unknown Type') }}</small>
                                        </div>
                                        <span class="brand-badge-modern">{{ vehicle.brand|default('N/A') }}</span>
                                    </div>
                                    {% if vehicle.picture %}
                                    <div class="vehicle-image mt-2">
                                        <img src="{{ vehicle.picture }}" alt="Vehicle" class="img-fluid rounded">
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Vehicle Details -->
                                <div class="vehicle-card-body">
                                    <!-- User ID Row -->
                                    <div class="detail-row-modern">
                                        <div class="detail-label-modern">
                                            <i class="fas fa-user text-primary"></i>
                                            <span>User ID</span>
                                        </div>
                                        <div class="detail-value-modern">
                                            <code class="value-code" id="userId-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ document.userId|default('N/A') }}</code>
                                            <button class="copy-btn-modern" onclick="copyToClipboard('userId-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy User ID">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Brand Row -->
                                    <div class="detail-row-modern">
                                        <div class="detail-label-modern">
                                            <i class="fas fa-tag text-success"></i>
                                            <span>Brand</span>
                                        </div>
                                        <div class="detail-value-modern">
                                            <code class="value-code" id="brand-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ vehicle.brand|default('N/A') }}</code>
                                            <button class="copy-btn-modern" onclick="copyToClipboard('brand-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy Brand">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Country Row -->
                                    <div class="detail-row-modern">
                                        <div class="detail-label-modern">
                                            <i class="fas fa-globe text-info"></i>
                                            <span>Country</span>
                                        </div>
                                        <div class="detail-value-modern">
                                            <code class="value-code" id="country-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ vehicle.country|default(document.profile.country|default('N/A')) }}</code>
                                            <button class="copy-btn-modern" onclick="copyToClipboard('country-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy Country">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- VIN Row -->
                                    <div class="detail-row-modern">
                                        <div class="detail-label-modern">
                                            <i class="fas fa-barcode text-warning"></i>
                                            <span>VIN</span>
                                        </div>
                                        <div class="detail-value-modern">
                                            <code class="value-code" id="vin-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ vehicle.vin|default('N/A') }}</code>
                                            <button class="copy-btn-modern" onclick="copyToClipboard('vin-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy VIN">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Vehicle Footer -->
                                <div class="vehicle-card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            {% if vehicle.mileage and vehicle.mileage.value %}
                                                <i class="fas fa-tachometer-alt mr-1"></i>{{ vehicle.mileage.value }} km
                                            {% else %}
                                                <i class="fas fa-info-circle mr-1"></i>No mileage data
                                            {% endif %}
                                        </small>
                                        <small class="text-muted">
                                            {% if vehicle.featureCode %}
                                                <i class="fas fa-cogs mr-1"></i>{{ vehicle.featureCode|length }} features
                                            {% else %}
                                                <i class="fas fa-minus-circle mr-1"></i>No features
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>
</div>
{% endif %}
