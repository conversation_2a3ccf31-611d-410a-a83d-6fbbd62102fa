{# Vehicles Display Component #}
{% if mongoData and mongoData.documents is not empty %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border-radius: 12px;">
    <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px 12px 0 0; border: none;">
        <h5 class="mb-0"><i class="fas fa-database mr-2"></i>Documents Found ({{ mongoData.documents|length }})</h5>
        <small style="color: rgba(255,255,255,0.8);">Vehicle information with copy functionality</small>
    </div>
    <div class="card-body" style="padding: 25px;">
        <div class="row">
            {% for document in mongoData.documents %}
                {% if document.vehicle is defined and document.vehicle is not empty %}
                    {% for vehicle in document.vehicle %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="vehicle-info-card">
                            <div class="vehicle-header">
                                <span class="brand-badge">{{ vehicle.brand|default('N/A') }}</span>
                                <div class="vehicle-title">{{ vehicle.shortLabel|default(vehicle.modelDescription|default('Vehicle')) }}</div>
                            </div>

                            <div class="vehicle-details">
                                <!-- User ID -->
                                <div class="detail-row">
                                    <div class="detail-label">
                                        <i class="fas fa-user mr-2"></i>User ID:
                                    </div>
                                    <div class="detail-value">
                                        <span class="value-text" id="userId-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ document.userId|default('N/A') }}</span>
                                        <button class="copy-btn" onclick="copyToClipboard('userId-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy User ID">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Brand -->
                                <div class="detail-row">
                                    <div class="detail-label">
                                        <i class="fas fa-tag mr-2"></i>Brand:
                                    </div>
                                    <div class="detail-value">
                                        <span class="value-text" id="brand-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ vehicle.brand|default('N/A') }}</span>
                                        <button class="copy-btn" onclick="copyToClipboard('brand-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy Brand">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Country -->
                                <div class="detail-row">
                                    <div class="detail-label">
                                        <i class="fas fa-globe mr-2"></i>Country:
                                    </div>
                                    <div class="detail-value">
                                        <span class="value-text" id="country-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ vehicle.country|default(document.profile.country|default('N/A')) }}</span>
                                        <button class="copy-btn" onclick="copyToClipboard('country-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy Country">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- VIN -->
                                <div class="detail-row">
                                    <div class="detail-label">
                                        <i class="fas fa-barcode mr-2"></i>VIN:
                                    </div>
                                    <div class="detail-value">
                                        <span class="value-text" id="vin-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">{{ vehicle.vin|default('N/A') }}</span>
                                        <button class="copy-btn" onclick="copyToClipboard('vin-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy VIN">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
