# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    http_method_override: false
    serializer:
        enabled: true
    handle_all_throwables: true
    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        handler_id: null
        #save_path: '%kernel.project_dir%/var/sessions'
        cookie_secure: auto
        cookie_samesite: lax
        storage_factory_id: session.storage.factory.native
        #gc_probability: 1
        #gc_divisor: 100
        #gc_maxlifetime: 3600 # 1 hour

    #esi: true
    #fragments: true
    php_errors:
        log: true

    annotations: false
    http_client:
        scoped_clients:
            mongo_atlas_client:
                base_uri: '%env(MONGO_ATLAS_BASE_URL)%'
                headers:
                    api-key: '%env(MONGO_ATLAS_API_KEY)%'
                    Content-Type: application/json
            mongo_atlas_client_2:  # New client
                base_uri: '%env(MONGO_ATLAS_BASE_URL_2)%'
                headers:
                    api-key: '%env(MONGO_ATLAS_API_KEY_2)%'
                    Content-Type: application/json
when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file