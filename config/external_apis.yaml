external_apis:
  base_url: "https://api-sys-sams-data-preprod.space.awsmpsa.com/"
  
  apis:
    catalog:
      name: "Catalog API"
      endpoint: "v1/catalog"
      method: "GET"
      description: "Retrieve catalog information for user and vehicle"
      required_params:
        - userId
        - country
        - language
        - brand
        - vin
      param_mapping:
        userId: "userId"
        country: "profile.country"
        language: "preferredDealer.{brand}.language"
        brand: "vehicle[0].brand"
        vin: "vehicle[0].vin"
      fallback_mapping:
        language: "preferredDealer.ap.language"  # fallback if brand-specific language not found
      
    subscription:
      name: "Subscription API"
      endpoint: "v1/subscription"
      method: "GET"
      description: "Get subscription details for user and vehicle"
      required_params:
        - userId
        - vin
      param_mapping:
        userId: "userId"
        vin: "vehicle[0].vin"
        
    # Future APIs can be added here
    # user_profile:
    #   name: "User Profile API"
    #   endpoint: "v1/user/profile"
    #   method: "GET"
    #   description: "Get user profile information"
    #   required_params:
    #     - userId
    #     - email
    #   param_mapping:
    #     userId: "userId"
    #     email: "profile.email"
    
    # vehicle_status:
    #   name: "Vehicle Status API"
    #   endpoint: "v1/vehicle/status"
    #   method: "GET"
    #   description: "Get current vehicle status"
    #   required_params:
    #     - vin
    #     - brand
    #   param_mapping:
    #     vin: "vehicle[0].vin"
    #     brand: "vehicle[0].brand"
