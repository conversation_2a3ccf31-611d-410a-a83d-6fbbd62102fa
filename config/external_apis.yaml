api_groups:
  sams_data:
    name: "SAMS Data API"
    description: "Space SAMS Data Management APIs"
    base_url: "https://api-sys-sams-data-preprod.space.awsmpsa.com"
    endpoints:
      catalog:
        name: "Catalog API"
        path: "/v1/catalog"
        method: "GET"
        description: "Retrieve catalog information for user and vehicle"
        parameters:
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"
          - name: "country"
            type: "string"
            required: true
            description: "Country code"
          - name: "language"
            type: "string"
            required: true
            description: "Language code"
          - name: "brand"
            type: "string"
            required: true
            description: "Vehicle brand"
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"

      subscription:
        name: "Subscription API"
        path: "/v1/subscription"
        method: "GET"
        description: "Get subscription details for user and vehicle"
        parameters:
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"

      user_profile:
        name: "User Profile API"
        path: "/v1/user/profile"
        method: "GET"
        description: "Get user profile information"
        parameters:
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"
          - name: "email"
            type: "string"
            required: false
            description: "User email address"

  vehicle_api:
    name: "Vehicle Management API"
    description: "Vehicle status and management endpoints"
    base_url: "https://api-vehicle-preprod.space.awsmpsa.com"
    endpoints:
      status:
        name: "Vehicle Status"
        path: "/v2/vehicle/status"
        method: "GET"
        description: "Get current vehicle status and information"
        parameters:
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"
          - name: "brand"
            type: "string"
            required: true
            description: "Vehicle brand code"
          - name: "country"
            type: "string"
            required: false
            description: "Country code"

      location:
        name: "Vehicle Location"
        path: "/v2/vehicle/location"
        method: "GET"
        description: "Get vehicle location information"
        parameters:
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"

      remote_control:
        name: "Remote Control"
        path: "/v2/vehicle/remote"
        method: "POST"
        description: "Send remote commands to vehicle"
        parameters:
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"
          - name: "command"
            type: "string"
            required: true
            description: "Remote command type"
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"

  charging_api:
    name: "Charging Management API"
    description: "Electric vehicle charging management"
    base_url: "https://api-charging-preprod.space.awsmpsa.com"
    endpoints:
      charging_status:
        name: "Charging Status"
        path: "/v1/charging/status"
        method: "GET"
        description: "Get current charging status"
        parameters:
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"

      start_charging:
        name: "Start Charging"
        path: "/v1/charging/start"
        method: "POST"
        description: "Start charging session"
        parameters:
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"
          - name: "target_soc"
            type: "integer"
            required: false
            description: "Target state of charge percentage"

      stop_charging:
        name: "Stop Charging"
        path: "/v1/charging/stop"
        method: "POST"
        description: "Stop charging session"
        parameters:
          - name: "vin"
            type: "string"
            required: true
            description: "Vehicle identification number"
          - name: "userId"
            type: "string"
            required: true
            description: "User identifier"
